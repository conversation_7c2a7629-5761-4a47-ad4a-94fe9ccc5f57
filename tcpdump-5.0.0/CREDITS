This file lists people who have contributed to tcpdump.

The current maintainers (in alphabetical order):
    <PERSON>                <denis at ovsienko dot info>
    <PERSON><PERSON><PERSON><PERSON>       <devel dot fx dot lebail at orange dot fr>
    <PERSON>                    <gharris at sonic dot net>
    <PERSON>            <mcr at sandelman dot ottawa dot on dot ca>

Additional people who have contributed patches (in alphabetical order):
    a1346054                      <36859588+a1346054 at users dot noreply dot github dot com>
    <PERSON>                <aaron at arbor dot net>
    ABHIMANYU                     <agupta07 at sourceforge dot net>
    <PERSON> <PERSON>                       <agcosta at gis dot net>
    <PERSON>                  <ats at offog dot org>
    <PERSON>              <ahabdels at gmail dot com>
    Ajith <PERSON>                   <adapa dot ajith at gmail dot com>
    <PERSON>                   <china at thewrittenword dot com>
    <PERSON>           <alexandra1975 at sourceforge dot net>
    <PERSON>            <alexandre dot ferrieux at orange dot com>
    <PERSON><PERSON><PERSON>           <alexandr dot nedvedicky at oracle dot com>
    <PERSON>              <alexis dot lagoutte at gmail dot com>
    <PERSON>                <a<PERSON><PERSON> at s21sec dot com>
    <PERSON>                <autostart dot ini at gmail dot com>
    <PERSON><PERSON><PERSON>          <anantha at juniper dot net>
    <PERSON>                 <a dot bittau at cs dot ucl dot ac dot uk>
    Andrea Ieri                   <andrea dot ieri at canonical dot com>
    Andreas <PERSON>aggi                 <andreas dot jaggi at waterwave dot ch>
    Andrew Brown                  <atatat at atatdot dot net>
    Andrew Church                 <andrew at users dot sourceforge dot net>
    Andrew Darqui                 <andrew dot darqui at gmail dot com>
    Andrew Hintz                  <adhintz at users dot sourceforge dot net>
    Andrew Lunn                   <andrew at lunn dot ch>
    Andrew Nording                <andrew at nording dot ru>
    Andrew Tridgell               <tridge at linuxcare dot com>
    Andy Heffernan                <ahh at juniper dot net>
    Angus Cameron                 <anguscc at yahoo dot com>
    Anton Bernal                  <anton at juniper dot net>
    Antonin Décimo                <antonin dot decimo at gmail dot com>
    Aravind Prasad S              <raja dot avi at gmail dot com>
    Arkadiusz Miskiewicz          <misiek at pld dot org dot pl>
    Armando L. Caro Jr.           <acaro at mail dot eecis dot udel dot edu>
    Arnaldo Carvalho de Melo      <acme at ghostprotocols dot net>
    Atsushi Onoe                  <onoe at netbsd dot org>
    Baptiste Jonglez              <baptiste dot jonglez at ens-lyon dot org>
    Baruch Siach                  <baruch at tkos dot co dot il>
    Ben Byer                      <bushing at sourceforge dot net>
    Ben Smithurst                 <ben at scientia dot demon dot co dot uk>
    Bert Vermeulen                <bert at biot dot com>
    Bill Fenner                   <fenner at gmail dot com>
    Bill Parker                   <wp02855 at gmail dot com>
    Bjoern A. Zeeb                <bzeeb at Zabbadoz dot NeT>
    Bram                          <tcpdump at mail dot wizbit dot be>
    Brent L. Bates                <blbates at vigyan dot com>
    Brian Carpenter               <brian dot carpenter at gmail dot com>
    Brian Ginsbach                <ginsbach at cray dot com>
    Brooks Davis                  <brooks at one-eyed-alien dot net>
    Bruce M. Simpson              <bms at spc dot org>
    Bryce Wood                    <woodbr at oregonstate dot edu>
    bugyo                         <bugyo at users dot noreply dot github dot com>
    Carles Kishimoto Bisbe        <ckishimo at ac dot upc dot es>
    Casey Deccio                  <casey at deccio dot net>
    Casper Andersson              <casper dot casan at gmail dot com>
    Charles (Chas) Williams       <chwillia at ciena dot com>
    Charles M. Hannum             <mycroft at netbsd dot org>
    Charlie Lenahan               <clenahan at fortresstech dot com>
    Chris Cogdon                  <chris at cogdon dot org>
    Chris G. Demetriou            <cgd at netbsd dot org>
    Chris Jepeway                 <jepeway at blasted-heath dot com>
    Chris Larson                  <clarson at kergoth dot com>
    Christian Sievers             <c_s at users dot sourceforge dot net>
    Christophe Rhodes             <csr21 at cantab dot net>
    Cliff Frey                    <cliff at meraki dot com>
    Colin Sames                   <sames dot colin at gmail dot com>
    Craig Leres                   <leres at xse dot com>
    Craig Rodrigues               <rodrigc at mediaone dot net>
    Crist J. Clark                <cjclark at alum dot mit dot edu>
    Dag-Erling Smørgrav           <des at FreeBSD dot org>
    Dagobert Michelsen            <dam at opencsw dot org>
    Daniel Hagerty                <hag at ai dot mit dot edu>
    Daniel Lee                    <Longinus00 at gmail dot com>
    Daniel Miller                 <dmiller at nmap dot org>
    Dario Lombardo                <lomato at gmail dot com>
    Darren Reed                   <darrenr at reed dot wattle dot id dot au>
    David Binderman               <d dot binderman at virgin dot net>
    David Cronin                  <davidcronin94 at gmail dot com>
    Davide Caratti                <dcaratti at redhat dot com>
    David Horn                    <dhorn2000 at gmail dot com>
    David Karoly                  <david dot karoly at outlook dot com>
    David Mirabito                <davidjm at arista dot com>
    David Smith                   <dsmith at redhat dot com>
    David Young                   <dyoung at ojctech dot com>
    Dion Bosschieter              <dbosschieter at transip dot nl>
    Dmitrij Tejblum               <tejblum at yandex-team dot ru>
    Dmitry Eremin-Solenikov       <dbaryshkov at gmail dot com>
    Dominique Martinet            <dominique dot martinet at atmark-techno dot com>
    Donatas Abraitis              <donatas dot abraitis at gmail dot com>
    Don Ebright                   <Don dot Ebright at compuware dot com>
    Dror Eiger                    <45061021+deiger at users dot noreply dot github dot com>
    d simonov                     <simonov-d at yandex-team dot ru>
    Duane Wessels                 <dwessels at verisign dot com>
    Eamon Doyle                   <eamonjd at arista dot com>
    Eddie Kohler                  <xexd at sourceforge dot net>
    Ed Maste                      <emaste at FreeBSD dot org>
    Ege Çetin                     <egecetin at hotmail dot com dot tr>
    Eliot Lear                    <lear at upstairs dot ofcourseimright dot com>
    Elmar Kirchner                <elmar at juniper dot net>
    Eric S. Raymond               <esr at thyrsus dot com>
    Etienne Marais                <etienne at marais dot green>
    Fang Wang                     <fangwang at sourceforge dot net>
    Ferry Huberts                 <ferry dot huberts at pelagic dot nl>
    Florent Drouin                <Florent dot Drouin at alcatel-lucent dot fr>
    Florian Fainelli              <f dot fainelli at gmail dot com>
    Florian Forster               <octo at verplant dot org>
    fra                           <foo at bar dot baz>
    Francesco Fondelli            <francesco dot fondelli at gmail dot com>
    Francisco Matias Cuenca-Acuna <mcuenca at george dot rutgers dot edu>
    Francis Dupont                <Francis dot Dupont at enst-bretagne dot fr>
    Frank Volf                    <volf at oasis dot IAEhv dot nl>
    Fulvio Risso                  <risso at polito dot it>
    George Bakos                  <gbakos at ists dot dartmouth dot edu>
    George Neville-Neil           <gnn at freebsd dot org>
    Gerald Combs                  <gerald at wireshark dot org>
    Gerard Garcia                 <ggarcia at deic dot uab dot cat>
    Gerrit Renker                 <gerrit at erg dot abdn dot ac dot uk>
    Gert Doering                  <gert at greenie dot muc dot de>
    Gianluca Varenni              <gianluca dot varenni at gmail dot com>
    Gilbert Ramirez Jr.           <gram at xiexie dot org>
    Gisle Vanem                   <gvanem at yahoo dot no>
    Gleb Smirnoff                 <glebius at FreeBSD dot org>
    Gokul Sivakumar               <gokulkumar792 at gmail dot com>
    Greg Minshall                 <minshall at acm dot org>
    Grégoire Henry                <henry at pps dot jussieu dot fr>
    Gregory Detal                 <gregory dot detal at uclouvain dot be>
    Greg Stark                    <gsstark at mit dot edu>
    Greg Steinbrecher             <steinbrecher at alum dot mit dot edu>
    Guy Lewin                     <guy at lewin dot co dot il>
    Hank Leininger                <tcpdump-workers at progressive-comp dot com>
    Hannes Gredler                <hannes at gredler dot at>
    Hannes Viertel                <hviertel at juniper dot net>
    Hanno Böck                    <hanno at hboeck dot de>
    Hans Petter Selasky           <hps at selasky dot org>
    Harry Raaymakers              <harryr at connect dot com dot au>
    Heinz-Ado Arnolds             <Ado dot Arnolds at dhm-systems dot de>
    Hendrik Scholz                <hendrik at scholz dot net>
    Herwin Weststrate             <herwin at quarantainenet dot nl>
    Ian McDonald                  <imcdnzl at gmail dot com>
    Ilpo Järvinen                 <ilpo dot jarvinen at helsinki dot fi>
    ishaangandhi                  <ishaangandhi at gmail dot com>
    Jacek Tobiasz                 <Jacek dot Tobiasz at atm dot com dot pl>
    Jacob Davis                   <jacobgb24 at yahoo dot com>
    Jakob Schlyter                <jakob at openbsd dot org>
    Jakub Zawadzki                <darkjames at darkjames dot pl>
    Jamal Hadi Salim              <hadi at cyberus dot ca>
    James Ko                      <jck at exegin dot com>
    Jamie Bainbridge              <jamie dot bainbridge at gmail dot com>
    Janne Heß                     <janne at hess dot ooo>
    Jan Oravec                    <wsx at wsx6 dot net>
    Jason L. Wright               <jason at thought dot net>
    Jason R. Thorpe               <thorpej at netbsd dot org>
    Jean-Raphaël Gaglione         <jr dot gaglione at yahoo dot fr>
    Jeff Chan                     <jchan at arista dot com>
    Jefferson Ogata               <jogata at nodc dot noaa dot gov>
    Jeffrey Hutzelman             <jhutz at cmu dot edu>
    Jeremy Browne                 <jer at ifni dot ca>
    Jerome Duval                  <jerome dot duval at gmail dot com>
    Jesper Peterson               <jesper at endace dot com>
    Jesse Gross                   <jesse at nicira dot com>
    Jesse Rosenstock              <jmr at google dot com>
    Jim Hutchins                  <jim at ca dot sandia dot gov>
    João Medeiros                 <ignotus21 at sourceforge dot net>
    Job Snijders                  <job at instituut dot net>
    Joerg Mayer                   <jmayer at loplof dot de>
    Jonas Chianu                  <jchianu at onx-jchianu-02 dot ciena dot com>
    Jonathan Heusser              <jonny at drugphish dot ch>
    Jorge Boncompte [DTI2]        <jorge at dti2 dot net>
    Jørgen Thomsen                <jth at jth dot net>
    Josh Soref                    <2119212+jsoref at users dot noreply dot github dot com>
    Julian Cowley                 <julian at lava dot net>
    Juliusz Chroboczek            <jch at pps dot jussieu dot fr>
    Kaarthik Sivakumar            <kaarthik at torrentnet dot com>
    Kaladhar Musunuru             <kaladharm at sourceforge dot net>
    Kamil Frankowicz              <kontakt at frankowicz dot me>
    Karl Norby                    <karl-norby at sourceforge dot net>
    Kazushi Sugyo                 <sugyo at pb dot jp dot nec dot com>
    Kelly Carmichael              <kcarmich at ipapp dot com>
    Ken Bantoft                   <ken at xelerance dot com>
    Ken Hornstein                 <kenh at cmf dot nrl dot navy dot mil>
    Kenichi Maehashi              <webmaster at kenichimaehashi dot com>
    Kevin Steves                  <stevesk at pobox dot com>
    Klaus Klein                   <kleink at reziprozitaet dot de>
    Kovarththanan Rajaratnam      <kovarththanan dot rajaratnam at gmail dot com>
    Kris Kennaway                 <kris at freebsd dot org>
    Krzysztof Halasa              <khc at pm dot waw dot pl>
    Larry Lile                    <lile at stdio dot com>
    Lennert Buytenhek             <buytenh at gnu dot org>
    Loganaden Velvindron          <logan at cyberstorm dot mu>
    Loris Degioanni               <loris at netgroup-serv dot polito dot it>
    Love Hörnquist-Åstrand        <lha at stacken dot kth dot se>
    Lucas C. Villa Real           <lucasvr at us dot ibm dot com>
    Luigi Rizzo                   <luigi at freebsd dot org>
    Luis MartinGarcia             <luis dot mgarc at gmail dot com>
    Luiz Otavio O Souza           <loos at freebsd dot org>
    Maciej W. Rozycki             <macro at ds2 dot pg dot gda dot pl>
    Manoharan Sundaramoorthy      <manoharan at arista dot com>
    Manu Pathak                   <mapathak at cisco dot com>
    Marc Abramowitz               <marc at marc-abramowitz dot com>
    Marc A. Lehmann               <pcg at goof dot com>
    Marc Binderberger             <mbind at sourceforge dot net>
    Mark Andrews                  <marka at isc dot org>
    Mark Ellzey Thomas            <mark at ackers dot net>
    Marko Kiiskila                <carnil at cs dot tut dot fi>
    Markus Schöpflin              <schoepflin at sourceforge dot net>
    Marshall Rose                 <mrose at dbc dot mtview dot ca dot us>
    Martin Buck                   <mb-tmp-tvguho dot pbz at gromit dot dyndns dot org>
    Martin Husemann               <martin at netbsd dot org>
    Martin Sehnoutka              <msehnout at redhat dot com>
    Martin Willi                  <martin at strongswan dot org>
    Matt Eaton                    <agnosticdev at gmail dot com>
    Matthew Luckie                <matthewluckie at sourceforge dot net>
    Matthew Martin                <phy1729 at gmail dot com>
    Matthias St. Pierre           <matthias dot st dot pierre at ncp-e dot com>
    Matthieu Boutier              <boutier at pps dot univ-paris-diderot dot fr>
    Max Laier                     <max at love2party dot net>
    Michael A. Meffie III         <meffie at sourceforge dot net>
    Michael Haardt                <michael at moria dot de>
    Michael Kirkhart              <michael dot kirkhart at att dot net>
    Michael Madore                <mmadore at turbolinux dot com>
    Michael Riepe                 <too-tired at sourceforge dot net>
    Michael Shalayeff             <mickey at openbsd dot org>
    Michael Shields               <shields at msrl dot com>
    Michael T. Stolarchuk         <mts at off dot to>
    Michal Ruprich                <michalruprich at gmail dot com>
    Michal Sekletar               <msekleta at redhat dot com>
    Michele "mydecay" Marchetto   <smarchetto1 at tin dot it>
    Mike Frysinger                <vapier at gmail dot com>
    Mingrui                       <972931182 at qq dot com>
    Minto Jeyananth               <minto at juniper dot net>
    Miroslav Lichvar              <mlichvar at redhat dot com>
    Mister X                      <3520734+Mister-X- at users dot noreply dot github dot com>
    Mitsunori Komatsu             <komamitsu at gmail dot com>
    Monroe Williams               <monroe at pobox dot com>
    Monthadar Al Jaberi           <monthadar at gmail dot com>
    Moses Devadason               <mosesdevadason at gmail dot com>
    Motonori Shindo               <mshindo at mshindo dot net>
    Nan Xiao                      <nan at chinadtrace dot org>
    Nathaniel Couper-Noles        <Nathaniel at isi1 dot tccisi dot com>
    Nathan J. Williams            <nathanw at MIT dot EDU>
    Nathan O'Sullivan             <nathan dot osullivan at mammoth dot com dot au>
    Neelabh                       <neelabhsahay at gmail dot com>
    Neil T. Spring                <bluehal at users dot sourceforge dot net>
    Nicholas Reilly               <nreilly at blackberry dot com>
    Nickolai Zeldovich            <kolya at MIT dot EDU>
    Nicolas Ferrero               <toorop at babylo dot net>
    Niels Provos                  <provos at openbsd dot org>
    Nikhil AP                     <nikhilap at arista dot com>
    Nikolay Edigaryev             <edigaryev at gmail dot com>
    niks3089                      <niks3089 at gmail dot com>
    Noritoshi Demizu              <demizu at users dot sourceforge dot net>
    Olaf Kirch                    <okir at caldera dot de>
    Ola Martin Lykkja             <ola dot lykkja at q-free dot com>
    Oleksij Rempel                <linux at rempel-privat dot de>
    Onno van der Linden           <onno at simplex dot nl>
    Paolo Abeni                   <paolo dot abeni at email dot it>
    Partha Ghosh                  <psg at cumulusnetworks dot com>
    Pascal Hennequin              <pascal dot hennequin at int-evry dot fr>
    Pasvorn Boonmark              <boonmark at juniper dot net>
    Patrik Lundquist              <patrik dot lundquist at gmail dot com>
    Paul Ferrell                  <pflarr at sourceforge dot net>
    Paul Mundt                    <lethal at linux-sh dot org>
    Paul S. Traina                <pst at freebsd dot org>
    Pavlin Radoslavov             <pavlin at icir dot org>
    Pawel Worach                  <pawel dot worach at gmail dot com>
    Pedro Monreal                 <pmgdeb at gmail dot com>
    Pekka Savola                  <pekkas at netcore dot fi>
    peppe                         <g1pi at libero dot it>
    Petar Alilovic                <petar dot alilovic at gmail dot com>
    Peter Fales                   <peter at fales-lorenz dot net>
    Peter Jeremy                  <peter dot jeremy at alcatel dot com dot au>
    Peter Krystad                 <peter dot krystad at linux dot intel dot com>
    Peter Volkov                  <pva at gentoo dot org>
    Petr Vorel                    <pvorel at suse dot cz>
                                  <pfhunt at users dot sourceforge dot net>
    Phil Wood                     <cpw at lanl dot gov>
    Pier Carlo Chiodi             <pierky at pierky dot com>
    Quentin Armitage              <quentin at armitage dot org dot uk>
    Rachid Tak Tak                <rachidtt at arista dot com>
    Rafal Maszkowski              <rzm at icm dot edu dot pl>
    Randy Sofia                   <rsofia at users dot sourceforge dot net>
    Raphael Raimbault             <raphael dot raimbault at netasq dot com>
    Renato Botelho                <garga at FreeBSD dot org>
    Ricardo Nabinger Sanchez      <rnsanchez at taghos dot com dot br>
    Richard Scheffenegger         <srichard at netapp dot com>
    Rick Cheng                    <rcheng at juniper dot net>
    Rick Jones                    <rick dot jones2 at hp dot com>
    Rick Watson                   <watsonrick at users dot sourceforge dot net>
    Ritesh Ranjan                 <r dot ranjan789 at gmail dot com>
    Rob Braun                     <bbraun at synack dot net>
    Robert Edmonds                <edmonds at debian dot org>
    Rocco Lucia                   <rlucia at iscanet dot com>
    Roderick Schertler            <roderick at argon dot org>
    Romain Francoise              <rfrancoise at debian dot org>
    Romero Malaquias              <romero dot malaquias at gmail dot com>
    Rose                          <83477269+AtariDreams at users dot noreply dot github dot com>
    Ruben Kerkhof                 <ruben at rubenkerkhof dot com>
    Rui Paulo                     <rpaulo at FreeBSD dot org>
    Rui                           <ruicunham at hotmail dot com>
    Sabrina Dubroca               <sd at queasysnail dot net>
    Sagun Shakya                  <sagun dot shakya at sun dot com>
    Sami Farin                    <safari at iki dot fi>
    Sam James                     <sam at gentoo dot org>
    Sascha Wildner                <swildner at sourceforge dot net>
    Sawssen Hadded                <saw dot hadded at gmail dot com>
    Scott Mcmillan                <scott dot a dot mcmillan at intel dot com>
    Scott Rose                    <syberpunk at users dot sourceforge dot net>
    Sebastian Krahmer             <krahmer at cs dot uni-potsdam dot de>
    Sebastien Raveau              <sebastien dot raveau at epita dot fr>
    Sebastien Vincent             <svincent at idems dot fr>
    Sepherosa Ziehau              <sepherosa at gmail dot com>
    Seth Webster                  <swebster at sst dot ll dot mit dot edu>
    Shinsuke Suzuki               <suz at kame dot net>
    Simon Nicolussi               <sinic at sinic dot name>
    Simon Ruderich                <simon at ruderich dot org>
    Slava Shwartsman              <slavash at mellanox dot com>
    Stefan Hajnoczi               <stefanha at redhat dot com>
    Steinar Haug                  <sthaug at nethelp dot no>
    Stephane Bortzmeyer           <stephane+github at bortzmeyer dot org>
    Steve Kay                     <stevekay at gmail dot com>
    Steven H. Wang                <wang dot steven dot h at gmail dot com>
    Steve-o                       <fnjordy at sourceforge dot net>
    Swaathi Vetrivel              <swaathiv at juniper dot net>
    Swaminathan Chandrasekaran    <chander at juniper dot net>
    Takashi Yamamoto              <yamt at mwd dot biglobe dot ne dot jp>
    Tatuya Jinmei                 <jinmei at kame dot net>
    Tero Kivinen                  <kivinen at iki dot fi>
    Terry Kennedy                 <terry at tmk dot com>
    test2                         <test2 at safs64 dot (none)>
    Thomas Jacob                  <jacob at internet24 dot de>
    Tillmann Karras               <tilkax at gmail dot com>
    Timo Koskiahde
    Tobias Waldekranz             <tobias at waldekranz dot com>
    Tom Jones                     <thj at freebsd dot org>
    Tommy Beadle                  <tbeadle at arbor dot net>
    Tony Li                       <tli at procket dot com>
    Tony Samuels                  <vegizombie at gmail dot com>
    Tony Xu                       <hhktony at gmail dot com>
    Toshihiro Kanda               <candy at fct dot kgc dot co dot jp>
    Udayakumar                    <udaya011 at gmail dot com>
    Ulrich Windl                  <Ulrich dot Windl at RZ dot Uni-Regensburg dot DE>
    Uns Lider                     <unslider at miranda dot org>
    Victor Oppleman               <oppleman at users dot sourceforge dot net>
    Viral Mehta                   <viral dot mehta at dell dot com>
    Vitaly Lavrov                 <vel21ripn at gmail dot com>
    Vivien Didelot                <vivien dot didelot at gmail dot com>
    Vyacheslav Trushkin           <dogonthesun at gmail dot com>
    Wang Jian                     <larkwang at gmail dot com>
    Weesan Lee                    <weesan at juniper dot net>
    Wesley Griffin                <wgriffin at users dot sourceforge dot net>
    Wesley Shields                <wxs at FreeBSD dot org>
    Wilbert de Graaf              <wilbertdg at hetnet dot nl>
    Will Drewry                   <will at alum dot bu dot edu>
    William J. Hulley             <bill dot hulley at gmail dot com>
    Wim Torfs                     <wtorfs at gmail dot com>
    Wolfgang Karall               <office at karall-edv dot at>
    Xin Li                        <delphij at FreeBSD dot org>
    yekm                          <yekm at h0me>
    Yen Yen Lim
    Yoshifumi Nishida
    zilog80a                      <zilog80a at sourceforge dot net>
    zolf                          <flos at xs4all dot nl>

The original LBL crew:
    Steve McCanne
    Craig Leres
    Van Jacobson

Past maintainers (in alphabetical order):
    Bill Fenner                   <fenner at gmail dot com>
    Fulvio Risso                  <risso at polito dot it>
    Hannes Gredler                <hannes at gredler dot at>
    Jun-ichiro itojun Hagino      <itojun at iijlab dot net>		Also see: http://www.wide.ad.jp/itojun-award/

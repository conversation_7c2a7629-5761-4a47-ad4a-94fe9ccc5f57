.\" Copyright (c) 1994, 1996, 1997
.\"	The Regents of the University of California.  All rights reserved.
.\"
.\" Redistribution and use in source and binary forms, with or without
.\" modification, are permitted provided that: (1) source code distributions
.\" retain the above copyright notice and this paragraph in its entirety, (2)
.\" distributions including binary code include the above copyright notice and
.\" this paragraph in its entirety in the documentation or other materials
.\" provided with the distribution, and (3) all advertising materials mentioning
.\" features or use of this software display the following acknowledgement:
.\" ``This product includes software developed by the University of California,
.\" Lawrence Berkeley Laboratory and its contributors.'' Neither the name of
.\" the University nor the names of its contributors may be used to endorse
.\" or promote products derived from this software without specific prior
.\" written permission.
.\" THIS SOFTWARE IS PROVIDED ``AS IS'' AND WITHOUT ANY EXPRESS OR IMPLIED
.\" WARRANTIES, INCLUDING, WITHOUT LIMITATION, THE IMPLIED WARRANTIES OF
.\" MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.
.\"
.TH PCAP_ACTIVATE 3PCAP "3 June 2024"
.SH NAME
pcap_activate \- activate a capture handle
.SH SYNOPSIS
.nf
.ft B
#include <pcap/pcap.h>
.ft
.LP
.ft B
int pcap_activate(pcap_t *p);
.ft
.fi
.SH DESCRIPTION
.BR pcap_activate ()
is used to activate a packet capture handle to look
at packets on the network, with the options that were set on the handle
being in effect.
.SH RETURN VALUE
.BR pcap_activate ()
returns
.B 0
on success without warnings, a non-zero positive value on
success with warnings, and a negative value on error.
A non-zero return value indicates what warning or error condition
occurred.
.LP
The possible warning values are:
.TP
.B PCAP_WARNING_PROMISC_NOTSUP
Promiscuous mode was requested, but the capture source doesn't support
promiscuous mode.
.TP
.B PCAP_WARNING_TSTAMP_TYPE_NOTSUP
The time stamp type specified in a previous
.BR pcap_set_tstamp_type (3PCAP)
call isn't supported by the capture source (the time stamp type is
left as the default),
.TP
.B PCAP_WARNING
Another warning condition occurred;
.BR pcap_geterr (3PCAP)
or
.BR pcap_perror (3PCAP)
may be called with
.I p
as an argument to fetch or display a message describing the warning
condition.
.LP
The possible error values are:
.TP
.B PCAP_ERROR_ACTIVATED
The handle has already been activated.
.TP
.B PCAP_ERROR_NO_SUCH_DEVICE
The capture source specified when the handle was created doesn't
exist.
.TP
.B PCAP_ERROR_PERM_DENIED
The process doesn't have permission to open the capture source.
.TP
.B PCAP_ERROR_PROMISC_PERM_DENIED
The process has permission to open the capture source but doesn't
have permission to put it into promiscuous mode.
.TP
.B PCAP_ERROR_RFMON_NOTSUP
Monitor mode was specified but the capture source doesn't support
monitor mode.
.TP
.B PCAP_ERROR_IFACE_NOT_UP
The capture source device is not up.
.TP
.B PCAP_ERROR_CAPTURE_NOTSUP
Packet capture is not supported on the capture source.
.TP
.B PCAP_ERROR
Another error occurred.
.BR pcap_geterr ()
or
.BR pcap_perror ()
may be called with
.I p
as an argument to fetch or display a message describing the error.
.LP
If
.BR PCAP_WARNING_PROMISC_NOTSUP ,
.BR PCAP_ERROR_NO_SUCH_DEVICE ,
.BR PCAP_ERROR_PERM_DENIED ,
or
.B PCAP_ERROR_CAPTURE_NOTSUP
is returned,
.BR pcap_geterr ()
or
.BR pcap_perror ()
may be called with
.I p
as an argument to fetch or display an message giving additional details
about the problem that might be useful for debugging the problem if it's
unexpected.
.LP
Additional warning and error codes may be added in the future; a program
should check for positive, negative, and zero return codes, and treat
all positive return codes as warnings and all negative return
codes as errors.
.BR pcap_statustostr (3PCAP)
can be called, with a warning or error code as an argument, to fetch a
message describing the warning or error code.
.LP
If
.BR pcap_activate ()
fails, the
.B pcap_t *
is not closed and freed; it should be closed using
.BR pcap_close (3PCAP).
.SH SEE ALSO
.BR pcap (3PCAP)

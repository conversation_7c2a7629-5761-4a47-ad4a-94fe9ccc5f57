.\" Copyright (c) 1994, 1996, 1997
.\"	The Regents of the University of California.  All rights reserved.
.\"
.\" Redistribution and use in source and binary forms, with or without
.\" modification, are permitted provided that: (1) source code distributions
.\" retain the above copyright notice and this paragraph in its entirety, (2)
.\" distributions including binary code include the above copyright notice and
.\" this paragraph in its entirety in the documentation or other materials
.\" provided with the distribution, and (3) all advertising materials mentioning
.\" features or use of this software display the following acknowledgement:
.\" ``This product includes software developed by the University of California,
.\" Lawrence Berkeley Laboratory and its contributors.'' Neither the name of
.\" the University nor the names of its contributors may be used to endorse
.\" or promote products derived from this software without specific prior
.\" written permission.
.\" THIS SOFTWARE IS PROVIDED ``AS IS'' AND WITHOUT ANY EXPRESS OR IMPLIED
.\" WARRANTIES, INCLUDING, WITHOUT LIMITATION, THE IMPLIED WARRANTIES OF
.\" MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.
.\"
.TH PCAP_OPEN_LIVE 3PCAP "11 March 2025"
.SH NAME
pcap_open_live \- open a device for capturing
.SH SYNOPSIS
.nf
.ft B
#include <pcap/pcap.h>
.ft
.LP
.nf
.ft B
char errbuf[PCAP_ERRBUF_SIZE];
.ft
.LP
.ft B
pcap_t *pcap_open_live(const char *device, int snaplen,
    int promisc, int to_ms, char *errbuf);
.ft
.fi
.SH DESCRIPTION
.BR pcap_open_live ()
is used to obtain a packet capture handle to capture packets on a device
(typically a network interface, see
.BR \%pcap_findalldevs (3PCAP)
for a more detailed explanation).
.I device
is a string that specifies the capture device to open, in this function
.B NULL
means the same as the string "any".
.PP
.I snaplen
specifies the snapshot length to be set on the handle.  If the packet
data should not be truncated at the end, a value of 262144 should be
sufficient for most devices, but D-Bus devices require a value of 128MiB
(128*1024*1024).
.PP
.I promisc
specifies whether the device is to be put into promiscuous mode.
If
.I promisc
is non-zero, promiscuous mode will be set, otherwise it will not be set.
.PP
.I to_ms
specifies the packet buffer timeout, as a non-negative value, in
milliseconds.  (See
.BR pcap (3PCAP)
for an explanation of the packet buffer timeout.)
.PP
.I errbuf
is a buffer large enough to hold at least
.B PCAP_ERRBUF_SIZE
chars.
.SH RETURN VALUE
.BR pcap_open_live ()
returns a
.B pcap_t *
on success and
.B NULL
on failure.
If
.B NULL
is returned,
.I errbuf
is filled in with an appropriate error message.
.I errbuf
may also be set to warning text when
.BR pcap_open_live ()
succeeds; to detect this case the caller should store a zero-length string in
.I errbuf
before calling
.BR pcap_open_live ()
and display the warning to the user if
.I errbuf
is no longer a zero-length string.
.SH SEE ALSO
.BR pcap_create (3PCAP),
.BR pcap_activate (3PCAP)

.\"
.\" Copyright (c) 1994, 1996, 1997
.\"	The Regents of the University of California.  All rights reserved.
.\"
.\" Redistribution and use in source and binary forms, with or without
.\" modification, are permitted provided that: (1) source code distributions
.\" retain the above copyright notice and this paragraph in its entirety, (2)
.\" distributions including binary code include the above copyright notice and
.\" this paragraph in its entirety in the documentation or other materials
.\" provided with the distribution, and (3) all advertising materials mentioning
.\" features or use of this software display the following acknowledgement:
.\" ``This product includes software developed by the University of California,
.\" Lawrence Berkeley Laboratory and its contributors.'' Neither the name of
.\" the University nor the names of its contributors may be used to endorse
.\" or promote products derived from this software without specific prior
.\" written permission.
.\" THIS SOFTWARE IS PROVIDED ``AS IS'' AND WITHOUT ANY EXPRESS OR IMPLIED
.\" WARRANTIES, INCLUDING, WITHOUT LIMITATION, THE IMPLIED WARRANTIES OF
.\" MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.
.\"
.TH PCAP_LIST_TSTAMP_TYPES 3PCAP "8 September 2019"
.SH NAME
pcap_list_tstamp_types, pcap_free_tstamp_types \- get a list of time
stamp types supported by a capture device, and free that list
.SH SYNOPSIS
.nf
.ft B
#include <pcap/pcap.h>
.ft
.LP
.ft B
int pcap_list_tstamp_types(pcap_t *p, int **tstamp_typesp);
void pcap_free_tstamp_types(int *tstamp_types);
.ft
.fi
.SH DESCRIPTION
.BR pcap_list_tstamp_types ()
is used to get a list of the supported time stamp types of the interface
associated with the pcap descriptor.
.BR pcap_list_tstamp_types ()
allocates an array to hold the list and sets
.I *tstamp_typesp
to point to the array.
See
.BR \%pcap-tstamp (@MAN_MISC_INFO@)
for a list of all the time stamp types.
.PP
The caller is responsible for freeing the array with
.BR pcap_free_tstamp_types (),
which frees the list pointed to by
.IR tstamp_types .
.SH RETURN VALUE
.BR pcap_list_tstamp_types ()
returns the number of time stamp types in the array on success and
.B PCAP_ERROR
on failure.
A return value of one means that the only time stamp type supported is
the one in the list, which is the capture device's default time stamp
type.  A return value of zero means that the only time stamp type
supported is
.BR PCAP_TSTAMP_HOST ,
which is the capture device's default time stamp type (only older
versions of libpcap will return that; newer versions will always return
one or more types).
If
.B PCAP_ERROR
is returned,
.BR pcap_geterr (3PCAP)
or
.BR pcap_perror (3PCAP)
may be called with
.I p
as an argument to fetch or display the error text.
.SH BACKWARD COMPATIBILITY
.PP
These functions became available in libpcap release 1.2.1.  In previous
releases, the time stamp type cannot be set; only the default time stamp
type offered by a capture source is available.
.SH SEE ALSO
.BR pcap (3PCAP),
.BR pcap_tstamp_type_val_to_name (3PCAP),
.BR pcap_set_tstamp_type (3PCAP),
.BR \%pcap-tstamp (@MAN_MISC_INFO@)

.\"
.\" Copyright (c) 1994, 1996, 1997
.\"	The Regents of the University of California.  All rights reserved.
.\"
.\" Redistribution and use in source and binary forms, with or without
.\" modification, are permitted provided that: (1) source code distributions
.\" retain the above copyright notice and this paragraph in its entirety, (2)
.\" distributions including binary code include the above copyright notice and
.\" this paragraph in its entirety in the documentation or other materials
.\" provided with the distribution, and (3) all advertising materials mentioning
.\" features or use of this software display the following acknowledgement:
.\" ``This product includes software developed by the University of California,
.\" Lawrence Berkeley Laboratory and its contributors.'' Neither the name of
.\" the University nor the names of its contributors may be used to endorse
.\" or promote products derived from this software without specific prior
.\" written permission.
.\" THIS SOFTWARE IS PROVIDED ``AS IS'' AND WITHOUT ANY EXPRESS OR IMPLIED
.\" WARRANTIES, INCLUDING, WITHOUT LIMITATION, THE IMPLIED WARRANTIES OF
.\" MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.
.\"
.TH PCAP_SET_TSTAMP_TYPE 3PCAP "8 September 2019"
.SH NAME
pcap_set_tstamp_type \- set the time stamp type to be used by a
capture device
.SH SYNOPSIS
.nf
.ft B
#include <pcap/pcap.h>
.ft
.LP
.ft B
int pcap_set_tstamp_type(pcap_t *p, int tstamp_type);
.ft
.fi
.SH DESCRIPTION
.BR pcap_set_tstamp_type ()
sets the type of time stamp desired for packets captured on the pcap
descriptor to the type specified by
.IR tstamp_type .
It must be called on a pcap descriptor created by
.BR pcap_create (3PCAP)
that has not yet been activated by
.BR pcap_activate (3PCAP).
.BR pcap_list_tstamp_types (3PCAP)
will give a list of the time stamp types supported by a given capture
device.
See
.BR \%pcap-tstamp (7)
for a list of all the time stamp types.
.SH RETURN VALUE
.BR pcap_set_tstamp_type ()
returns
.B 0
on success if the specified time stamp type is expected to be
supported by the capture device,
.B PCAP_WARNING_TSTAMP_TYPE_NOTSUP
if the specified time stamp type is not supported by the
capture device,
.B PCAP_ERROR_ACTIVATED
if called on a capture handle that has been activated, and
.B PCAP_ERROR_CANTSET_TSTAMP_TYPE
if the capture device doesn't support setting the time stamp type (only
older versions of libpcap will return that; newer versions will always
allow the time stamp type to be set to the default type).
.SH BACKWARD COMPATIBILITY
.PP
This function became available in libpcap release 1.2.1.  In previous
releases, the time stamp type cannot be set; only the default time stamp
type offered by a capture source is available.
.SH SEE ALSO
.BR pcap (3PCAP),
.BR pcap_list_tstamp_types (3PCAP),
.BR pcap_set_tstamp_precision (3PCAP),
.BR pcap_tstamp_type_name_to_val (3PCAP)

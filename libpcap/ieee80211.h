/*-
 * Copyright (c) 2001 <PERSON><PERSON>hi <PERSON>oe
 * Copyright (c) 2002-2005 <PERSON>, <PERSON><PERSON><PERSON> Consulting
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 * 1. Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 * 2. Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 * 3. The name of the author may not be used to endorse or promote products
 *    derived from this software without specific prior written permission.
 *
 * Alternatively, this software may be distributed under the terms of the
 * GNU General Public License ("GPL") version 2 as published by the Free
 * Software Foundation.
 *
 * THIS SOFTWARE IS PROVIDED BY THE AUTHOR ``AS IS'' AND ANY EXPRESS OR
 * IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.
 * IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY DIRECT, INDIRECT,
 * INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT
 * NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
 * DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
 * THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 * (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF
 * THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 * $FreeBSD: src/sys/net80211/ieee80211.h,v 1.10 2005/07/22 16:55:27 sam Exp $
 */
#ifndef _NET80211_IEEE80211_H_
#define _NET80211_IEEE80211_H_

/*
 * 802.11 protocol definitions.
 */

#define	IEEE80211_FC0_VERSION_MASK		0x03U
#define	IEEE80211_FC0_VERSION_SHIFT		0
#define	IEEE80211_FC0_VERSION_0			0x00U
#define	IEEE80211_FC0_TYPE_MASK			0x0cU
#define	IEEE80211_FC0_TYPE_SHIFT		2
#define	IEEE80211_FC0_TYPE_MGT			0x00U
#define	IEEE80211_FC0_TYPE_CTL			0x04U
#define	IEEE80211_FC0_TYPE_DATA			0x08U

#define	IEEE80211_FC0_SUBTYPE_MASK		0xf0U
#define	IEEE80211_FC0_SUBTYPE_SHIFT		4
/* for TYPE_MGT */
#define	IEEE80211_FC0_SUBTYPE_ASSOC_REQ		0x00U
#define	IEEE80211_FC0_SUBTYPE_ASSOC_RESP	0x10U
#define	IEEE80211_FC0_SUBTYPE_REASSOC_REQ	0x20U
#define	IEEE80211_FC0_SUBTYPE_REASSOC_RESP	0x30U
#define	IEEE80211_FC0_SUBTYPE_PROBE_REQ		0x40U
#define	IEEE80211_FC0_SUBTYPE_PROBE_RESP	0x50U
#define	IEEE80211_FC0_SUBTYPE_BEACON		0x80U
#define	IEEE80211_FC0_SUBTYPE_ATIM		0x90U
#define	IEEE80211_FC0_SUBTYPE_DISASSOC		0xa0U
#define	IEEE80211_FC0_SUBTYPE_AUTH		0xb0U
#define	IEEE80211_FC0_SUBTYPE_DEAUTH		0xc0U
/* for TYPE_CTL */
#define	IEEE80211_FC0_SUBTYPE_BAR		0x80U
#define	IEEE80211_FC0_SUBTYPE_BA		0x90U
#define	IEEE80211_FC0_SUBTYPE_PS_POLL		0xa0U
#define	IEEE80211_FC0_SUBTYPE_RTS		0xb0U
#define	IEEE80211_FC0_SUBTYPE_CTS		0xc0U
#define	IEEE80211_FC0_SUBTYPE_ACK		0xd0U
#define	IEEE80211_FC0_SUBTYPE_CF_END		0xe0U
#define	IEEE80211_FC0_SUBTYPE_CF_END_ACK	0xf0U
/* for TYPE_DATA (bit combination) */
#define	IEEE80211_FC0_SUBTYPE_DATA		0x00U
#define	IEEE80211_FC0_SUBTYPE_CF_ACK		0x10U
#define	IEEE80211_FC0_SUBTYPE_CF_POLL		0x20U
#define	IEEE80211_FC0_SUBTYPE_CF_ACPL		0x30U
#define	IEEE80211_FC0_SUBTYPE_NODATA		0x40U
#define	IEEE80211_FC0_SUBTYPE_NODATA_CF_ACK	0x50U
#define	IEEE80211_FC0_SUBTYPE_NODATA_CF_POLL	0x60U
#define	IEEE80211_FC0_SUBTYPE_NODATA_CF_ACPL	0x70U
#define	IEEE80211_FC0_SUBTYPE_QOS		0x80U
#define	IEEE80211_FC0_SUBTYPE_QOS_NULL		0xc0U

#define	IEEE80211_FC1_DIR_MASK			0x03U
#define	IEEE80211_FC1_DIR_NODS			0x00U	/* STA->STA */
#define	IEEE80211_FC1_DIR_TODS			0x01U	/* STA->AP  */
#define	IEEE80211_FC1_DIR_FROMDS		0x02U	/* AP ->STA */
#define	IEEE80211_FC1_DIR_DSTODS		0x03U	/* AP ->AP  */

#define	IEEE80211_FC1_MORE_FRAG			0x04U
#define	IEEE80211_FC1_RETRY			0x08U
#define	IEEE80211_FC1_PWR_MGT			0x10U
#define	IEEE80211_FC1_MORE_DATA			0x20U
#define	IEEE80211_FC1_WEP			0x40U
#define	IEEE80211_FC1_ORDER			0x80U

#define	IEEE80211_SEQ_FRAG_MASK			0x000fU
#define	IEEE80211_SEQ_FRAG_SHIFT		0
#define	IEEE80211_SEQ_SEQ_MASK			0xfff0U
#define	IEEE80211_SEQ_SEQ_SHIFT			4

#define	IEEE80211_NWID_LEN			32

#define	IEEE80211_QOS_TXOP			0x00ffU
/* bit 8 is reserved */
#define	IEEE80211_QOS_ACKPOLICY			0x60U
#define	IEEE80211_QOS_ACKPOLICY_S		5
#define	IEEE80211_QOS_ESOP			0x10U
#define	IEEE80211_QOS_ESOP_S			4
#define	IEEE80211_QOS_TID			0x0fU

#define IEEE80211_MGT_SUBTYPE_NAMES {			\
	"assoc-req",		"assoc-resp",		\
	"reassoc-req",		"reassoc-resp",		\
	"probe-req",		"probe-resp",		\
	"reserved#6",		"reserved#7",		\
	"beacon",		"atim",			\
	"disassoc",		"auth",			\
	"deauth",		"reserved#13",		\
	"reserved#14",		"reserved#15"		\
}

#define IEEE80211_CTL_SUBTYPE_NAMES {			\
	"reserved#0",		"reserved#1",		\
	"reserved#2",		"reserved#3",		\
	"reserved#3",		"reserved#5",		\
	"reserved#6",		"reserved#7",		\
	"bar",			"ba",			\
	"ps-poll",		"rts",			\
	"cts",			"ack",			\
	"cf-end",		"cf-end-ack"		\
}

#define IEEE80211_DATA_SUBTYPE_NAMES {			\
	"data",			"data-cf-ack",		\
	"data-cf-poll",		"data-cf-ack-poll",	\
	"null",			"cf-ack",		\
	"cf-poll",		"cf-ack-poll",		\
	"qos-data",		"qos-data-cf-ack",	\
	"qos-data-cf-poll",	"qos-data-cf-ack-poll",	\
	"qos",			"reserved#13",		\
	"qos-cf-poll",		"qos-cf-ack-poll"	\
}

#define IEEE80211_TYPE_NAMES	{ "mgt", "ctl", "data", "reserved#4" }

#endif /* _NET80211_IEEE80211_H_ */

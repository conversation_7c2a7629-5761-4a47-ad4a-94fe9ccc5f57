.\" Copyright (c) 1994, 1996, 1997
.\"	The Regents of the University of California.  All rights reserved.
.\"
.\" Redistribution and use in source and binary forms, with or without
.\" modification, are permitted provided that: (1) source code distributions
.\" retain the above copyright notice and this paragraph in its entirety, (2)
.\" distributions including binary code include the above copyright notice and
.\" this paragraph in its entirety in the documentation or other materials
.\" provided with the distribution, and (3) all advertising materials mentioning
.\" features or use of this software display the following acknowledgement:
.\" ``This product includes software developed by the University of California,
.\" Lawrence Berkeley Laboratory and its contributors.'' Neither the name of
.\" the University nor the names of its contributors may be used to endorse
.\" or promote products derived from this software without specific prior
.\" written permission.
.\" THIS SOFTWARE IS PROVIDED ``AS IS'' AND WITHOUT ANY EXPRESS OR IMPLIED
.\" WARRANTIES, INCLUDING, WITHOUT LIMITATION, THE IMPLIED WARRANTIES OF
.\" MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.
.\"
.TH PCAP_LIB_VERSION 3PCAP "22 December 2024"
.SH NAME
pcap_lib_version \- get the version information for libpcap
.SH SYNOPSIS
.nf
.ft B
#include <pcap/pcap.h>
.ft
.LP
.ft B
const char *pcap_lib_version(void);
.ft
.fi
.SH DESCRIPTION
.BR pcap_lib_version ()
returns a pointer to a string giving information about the version of
the libpcap library being used.  The string begins with the words "libpcap
version".  Then follows the version, which consists of three dot-separated
numbers and the optional "-PRE-GIT" suffix to indicate a development
snapshot, same as in the output of
.B "pcap-config --version"
in sufficiently recent libpcap releases.  After the version often follows
additional text to indicate particular features.  For example:
.IP "an old release, on any OS"
.RS
.nf
.B
libpcap version 1.5.3
.fi
.RE
.IP "a newer release on a Linux host"
.RS
.nf
.B
libpcap version 1.10.4 (with TPACKET_V3)
.fi
.RE
.IP "the same, built with DAG support only"
.RS
.nf
.B
libpcap version 1.10.4 (DAG-only)
.fi
.RE
.IP "a development snapshot on a FreeBSD host"
.RS
.nf
.B
libpcap version 1.11.0-PRE-GIT (with zerocopy support)
.fi
.RE
.SH SEE ALSO
.BR pcap\-config (1),
.BR pcap (3PCAP)

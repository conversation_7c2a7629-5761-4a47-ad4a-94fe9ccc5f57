.\" Copyright (c) 1987, 1988, 1989, 1990, 1991, 1992, 1994, 1995, 1996, 1997
.\"	The Regents of the University of California.  All rights reserved.
.\" All rights reserved.
.\"
.\" Redistribution and use in source and binary forms, with or without
.\" modification, are permitted provided that: (1) source code distributions
.\" retain the above copyright notice and this paragraph in its entirety, (2)
.\" distributions including binary code include the above copyright notice and
.\" this paragraph in its entirety in the documentation or other materials
.\" provided with the distribution, and (3) all advertising materials mentioning
.\" features or use of this software display the following acknowledgement:
.\" ``This product includes software developed by the University of California,
.\" Lawrence Berkeley Laboratory and its contributors.'' Neither the name of
.\" the University nor the names of its contributors may be used to endorse
.\" or promote products derived from this software without specific prior
.\" written permission.
.\" THIS SOFTWARE IS PROVIDED ``AS IS'' AND WITHOUT ANY EXPRESS OR IMPLIED
.\" WARRANTIES, INCLUDING, WITHOUT LIMITATION, THE IMPLIED WARRANTIES OF
.\" MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.
.\"
.TH PCAP\-CONFIG 1 "22 December 2024"
.SH NAME
pcap-config \- write libpcap compiler and linker flags to standard output
.SH SYNOPSIS
.na
.B pcap-config
[
.B \-\-help
]
[
.B \-\-version
]
[
.B \-\-cflags
]
.ti +12
[
.B \-\-libs
|
.B \-\-additional\-libs
]
.ti +12
[
.B \-\-static
|
.B \-\-static\-pcap\-only
]
.ad

.SH DESCRIPTION
.LP
.I pcap\-config
writes to the standard output various compiler and linker flags required to
build a user program with libpcap.  By default, it writes flags appropriate
for building with a dynamically\-linked version of libpcap; see below
for static linking.  Depending on the manner of libpcap installation, some
options or their combinations may produce empty output \- this is by design.

.SH OPTIONS
.TP
.B \-\-help
Produce a help message and exit.

.TP
.B \-\-version
Produce libpcap version and exit.  The version is the contents of
.I VERSION
file in libpcap source tree rather than the result of
.BR \%pcap_lib_version (3PCAP).

.TP
.B \-\-cflags
Produce the
.B \-I
compiler flag required to include libpcap's header files.

.TP
.B \-\-libs
Produce the
.B \-L
and
.B \-l
linker flags required to link with libpcap, including
.B \-l
flags for libraries required by libpcap.

.TP
.B \-\-additional\-libs
Produce the
.B \-L
and
.B \-l
linker flags for libraries required by libpcap, but not the
.B \-l
flag to link with libpcap itself.

.TP
.B \-\-static
This option causes
.B \-\-libs
and
.B \-\-additional\-libs
to produce linker flags appropriate for static linking with libpcap.

.TP
.B \-\-static\-pcap\-only
This option causes
.B \-\-libs
and
.B \-\-additional\-libs
to produce linker flags appropriate for static linking with libpcap and
dynamic linking with all other libraries, including libraries required by
libpcap.

.SH EXIT STATUS
.I pcap\-config
exits with a non-zero status when invoked with an invalid command\-line
option, and with status 0 otherwise.

.SH BACKWARD COMPATIBILITY
.PP
The
.B \-\-version
flag became available in libpcap release 1.10.3.
.PP
Before libpcap release 1.10.2
.I pcap\-config
did not treat invalid command\-line options as an error. The
.B \-\-static\-pcap\-only
flag became available in libpcap release 1.10.2.
.PP
The
.B \-\-static
flag became available in libpcap release 1.1.0.

.SH SEE ALSO
.BR pkg\-config (1),
.BR pcap (3PCAP)

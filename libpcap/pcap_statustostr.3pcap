.\" Copyright (c) 1994, 1996, 1997
.\"	The Regents of the University of California.  All rights reserved.
.\"
.\" Redistribution and use in source and binary forms, with or without
.\" modification, are permitted provided that: (1) source code distributions
.\" retain the above copyright notice and this paragraph in its entirety, (2)
.\" distributions including binary code include the above copyright notice and
.\" this paragraph in its entirety in the documentation or other materials
.\" provided with the distribution, and (3) all advertising materials mentioning
.\" features or use of this software display the following acknowledgement:
.\" ``This product includes software developed by the University of California,
.\" Lawrence Berkeley Laboratory and its contributors.'' Neither the name of
.\" the University nor the names of its contributors may be used to endorse
.\" or promote products derived from this software without specific prior
.\" written permission.
.\" THIS SOFTWARE IS PROVIDED ``AS IS'' AND WITHOUT ANY EXPRESS OR IMPLIED
.\" WARRANTIES, INCLUDING, WITHOUT LIMITATION, THE IMPLIED WARRANTIES OF
.\" MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.
.\"
.TH PCAP_STATUSTOSTR 3PCAP "3 January 2014"
.SH NAME
pcap_statustostr \- convert a PCAP_ERROR_ or PCAP_WARNING_ value to a string
.SH SYNOPSIS
.nf
.ft B
#include <pcap/pcap.h>
.ft
.LP
.ft B
const char *pcap_statustostr(int error);
.ft
.fi
.SH DESCRIPTION
.BR pcap_statustostr ()
converts a
.B PCAP_ERROR_
or
.B PCAP_WARNING_
value returned by a libpcap routine to an error string.
.SH SEE ALSO
.BR pcap (3PCAP)

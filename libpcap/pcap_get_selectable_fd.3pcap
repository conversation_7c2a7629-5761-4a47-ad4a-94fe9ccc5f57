.\" Copyright (c) 1994, 1996, 1997
.\"	The Regents of the University of California.  All rights reserved.
.\"
.\" Redistribution and use in source and binary forms, with or without
.\" modification, are permitted provided that: (1) source code distributions
.\" retain the above copyright notice and this paragraph in its entirety, (2)
.\" distributions including binary code include the above copyright notice and
.\" this paragraph in its entirety in the documentation or other materials
.\" provided with the distribution, and (3) all advertising materials mentioning
.\" features or use of this software display the following acknowledgement:
.\" ``This product includes software developed by the University of California,
.\" Lawrence Berkeley Laboratory and its contributors.'' Neither the name of
.\" the University nor the names of its contributors may be used to endorse
.\" or promote products derived from this software without specific prior
.\" written permission.
.\" THIS SOFTWARE IS PROVIDED ``AS IS'' AND WITHOUT ANY EXPRESS OR IMPLIED
.\" WARRANTIES, INCLUDING, WITHOUT LIMITATION, THE IMPLIED WARRANTIES OF
.\" MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.
.\"
.TH PCAP_GET_SELECTABLE_FD 3PCAP "29 January 2020"
.SH NAME
pcap_get_selectable_fd \- get a file descriptor on which a select() can
be done for a live capture
.SH SYNOPSIS
.nf
.ft B
#include <pcap/pcap.h>
.ft
.LP
.ft B
int pcap_get_selectable_fd(pcap_t *p);
.ft
.fi
.SH DESCRIPTION
.BR pcap_get_selectable_fd ()
returns, on UNIX, a file descriptor number for a file descriptor on
which one can
do a
.BR select (2),
.BR poll (2),
.BR epoll_wait (2),
.BR kevent (2),
or other such call
to wait for it to be possible to read packets without blocking, if such
a descriptor exists, or
.BR \-1 ,
if no such descriptor exists.
.PP
Some network devices opened with
.BR pcap_create (3PCAP)
and
.BR pcap_activate (3PCAP),
or with
.BR pcap_open_live (3PCAP),
do not support those calls (for example, regular network devices on
FreeBSD 4.3 and 4.4, and Endace DAG devices), so
.B \-1
is returned for
those devices.  In that case, those calls must be given a timeout less
than or equal to the timeout returned by
.BR pcap_get_required_select_timeout (3PCAP)
for the device for which
.BR pcap_get_selectable_fd ()
returned
.BR \-1 ,
the device must be put in non-blocking mode with a call to
.BR \%pcap_setnonblock (3PCAP),
and an attempt must always be made to read packets from the device
when the call returns.  If
.BR \%pcap_get_required_select_timeout ()
returns
.BR NULL ,
it is not possible to wait for packets to arrive on the device in an
event loop.
.PP
Note that a device on which a read can be done without blocking may,
on some platforms, not have any packets to read if the packet buffer
timeout has expired.  A call to
.BR pcap_dispatch (3PCAP)
or
.BR pcap_next_ex (3PCAP)
will return 0 in this case, but will not block.
.PP
Note that in:
.IP
FreeBSD prior to FreeBSD 4.6;
.IP
NetBSD prior to NetBSD 3.0;
.IP
OpenBSD prior to OpenBSD 2.4;
.IP
Mac OS X prior to Mac OS X 10.7;
.PP
.BR select (),
.BR poll (),
and
.BR kevent ()
do not work correctly on BPF devices;
.BR pcap_get_selectable_fd ()
will return a file descriptor on most of those versions (the exceptions
being FreeBSD 4.3 and 4.4), but a simple
.BR select (),
.BR poll (),
or
.BR kevent ()
call will not indicate that the descriptor is readable until a full
buffer's worth of packets is received, even if the packet timeout
expires before then.  To work around this, code that uses
those calls to wait for packets to arrive must put the
.B pcap_t
in non-blocking mode, and must arrange that the call
have a timeout less than or equal to the packet buffer timeout,
and must try to read packets after that timeout expires, regardless of
whether the call indicated that the file descriptor for the
.B pcap_t
is ready to be read or not.  (That workaround will not work in FreeBSD
4.3 and later; however, in FreeBSD 4.6 and later, those calls
work correctly on BPF devices, so the workaround isn't necessary,
although it does no harm.)
.PP
Note also that
.BR poll ()
and
.BR kevent ()
doesn't work on character special files, including BPF devices, in Mac
OS X 10.4 and 10.5, so, while
.BR select ()
can be used on the descriptor returned by
.BR pcap_get_selectable_fd (),
.BR poll ()
and
.BR kevent ()
cannot be used on it those versions of Mac OS X.
.BR poll (),
but not
.BR kevent (),
works on that descriptor in Mac OS X releases prior to
10.4;
.BR poll ()
and
.BR kevent ()
work on that descriptor in Mac OS X 10.6 and later.
.PP
.BR pcap_get_selectable_fd ()
is not available on Windows.
.SH RETURN VALUE
A selectable file descriptor is returned if one exists; otherwise,
.B \-1
is returned.
.SH SEE ALSO
.BR pcap (3PCAP),
.BR kqueue (2)

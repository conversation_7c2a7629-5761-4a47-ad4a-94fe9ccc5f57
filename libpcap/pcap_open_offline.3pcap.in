.\" Copyright (c) 1994, 1996, 1997
.\"	The Regents of the University of California.  All rights reserved.
.\"
.\" Redistribution and use in source and binary forms, with or without
.\" modification, are permitted provided that: (1) source code distributions
.\" retain the above copyright notice and this paragraph in its entirety, (2)
.\" distributions including binary code include the above copyright notice and
.\" this paragraph in its entirety in the documentation or other materials
.\" provided with the distribution, and (3) all advertising materials mentioning
.\" features or use of this software display the following acknowledgement:
.\" ``This product includes software developed by the University of California,
.\" Lawrence Berkeley Laboratory and its contributors.'' Neither the name of
.\" the University nor the names of its contributors may be used to endorse
.\" or promote products derived from this software without specific prior
.\" written permission.
.\" THIS SOFTWARE IS PROVIDED ``AS IS'' AND WITHOUT ANY EXPRESS OR IMPLIED
.\" WARRANTIES, INCLUDING, WITHOUT LIMITATION, THE IMPLIED WARRANTIES OF
.\" MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.
.\"
.TH PCAP_OPEN_OFFLINE 3PCAP "12 October 2024"
.SH NAME
pcap_open_offline, pcap_open_offline_with_tstamp_precision,
pcap_fopen_offline, pcap_fopen_offline_with_tstamp_precision \- open a saved capture file for reading
.SH SYNOPSIS
.nf
.ft B
#include <pcap/pcap.h>
.ft
.LP
.nf
.ft B
char errbuf[PCAP_ERRBUF_SIZE];
.ft
.LP
.ft B
pcap_t *pcap_open_offline(const char *fname, char *errbuf);
pcap_t *pcap_open_offline_with_tstamp_precision(const char *fname,
    u_int precision, char *errbuf);
pcap_t *pcap_fopen_offline(FILE *fp, char *errbuf);
pcap_t *pcap_fopen_offline_with_tstamp_precision(FILE *fp,
    u_int precision, char *errbuf);
.ft
.fi
.SH DESCRIPTION
.BR pcap_open_offline ()
and
.BR pcap_open_offline_with_tstamp_precision ()
are called to open a ``savefile'' for reading.
.PP
.I fname
specifies the name of the file to open. The file can have the pcap file
format as described in
.BR \%pcap-savefile (@MAN_FILE_FORMATS@),
which is the file format used by, among other programs,
.BR tcpdump (1)
and
.BR tcpslice (1),
or can have the pcapng file format, although not all pcapng files can
be read.
The name "-" is a synonym for
.BR stdin .
.PP
.BR pcap_open_offline_with_tstamp_precision ()
takes an additional
.I precision
argument specifying the time stamp precision desired;
if
.B PCAP_TSTAMP_PRECISION_MICRO
is specified, packet time stamps will be supplied in seconds and
microseconds,
and if
.B PCAP_TSTAMP_PRECISION_NANO
is specified, packet time stamps will be supplied in seconds and
nanoseconds.  If the time stamps in the file do not have the same
precision as the requested precision, they will be scaled up or down as
necessary before being supplied.
.PP
Alternatively, you may call
.BR pcap_fopen_offline ()
or
.BR pcap_fopen_offline_with_tstamp_precision ()
to read dumped data from an existing open stream
.IR fp ;
this stream will be closed by a subsequent call to
.BR pcap_close (3PCAP)
unless it is
.BR stdin .
.BR pcap_fopen_offline_with_tstamp_precision ()
takes an additional
.I precision
argument as described above.
Note that on Windows, that stream should be opened in binary mode.
.PP
.I errbuf
is a buffer large enough to hold at least
.B PCAP_ERRBUF_SIZE
chars.
.SH RETURN VALUE
.BR pcap_open_offline (),
.BR pcap_open_offline_with_tstamp_precision (),
.BR pcap_fopen_offline (),
and
.BR pcap_fopen_offline_with_tstamp_precision ()
return a
.B pcap_t *
on success and
.B NULL
on failure.
If
.B NULL
is returned,
.I errbuf
is filled in with an appropriate error message.
.SH BACKWARD COMPATIBILITY
.BR pcap_open_offline_with_tstamp_precision ()
and
.BR pcap_fopen_offline_with_tstamp_precision ()
became available in libpcap release 1.5.1.  In previous releases, time
stamps from a savefile are always given in seconds and microseconds.
.SH SEE ALSO
.BR pcap (3PCAP),
.BR \%pcap-savefile (@MAN_FILE_FORMATS@)

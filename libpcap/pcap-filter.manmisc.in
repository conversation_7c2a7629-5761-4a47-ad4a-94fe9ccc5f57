.\" Copyright (c) 1987, 1988, 1989, 1990, 1991, 1992, 1994, 1995, 1996, 1997
.\"	The Regents of the University of California.  All rights reserved.
.\" All rights reserved.
.\"
.\" Redistribution and use in source and binary forms, with or without
.\" modification, are permitted provided that: (1) source code distributions
.\" retain the above copyright notice and this paragraph in its entirety, (2)
.\" distributions including binary code include the above copyright notice and
.\" this paragraph in its entirety in the documentation or other materials
.\" provided with the distribution, and (3) all advertising materials mentioning
.\" features or use of this software display the following acknowledgement:
.\" ``This product includes software developed by the University of California,
.\" Lawrence Berkeley Laboratory and its contributors.'' Neither the name of
.\" the University nor the names of its contributors may be used to endorse
.\" or promote products derived from this software without specific prior
.\" written permission.
.\" THIS SOFTWARE IS PROVIDED ``AS IS'' AND WITHOUT ANY EXPRESS OR IMPLIED
.\" WARRANTIES, INCLUDING, WITHOUT LIMITATION, THE IMPLIED WARRANTIES OF
.\" MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.
.\"
.TH PCAP-FILTER @MAN_MISC_INFO@ "7 April 2025"
.SH NAME
pcap-filter \- packet filter syntax
.br
.ad
.SH DESCRIPTION
.LP
.BR pcap_compile (3PCAP)
is used to compile a string into a filter program.
The resulting filter program can then be applied to
some stream of packets to determine which packets will be supplied to
.BR pcap_loop (3PCAP),
.BR pcap_dispatch (3PCAP),
.BR pcap_next (3PCAP),
or
.BR pcap_next_ex (3PCAP).
.LP
The \fIfilter expression\fP consists of one or more
.IR primitives .
Primitives usually consist of an
.I id
(a name, a number or something slightly more complex, such as a CIDR prefix)
preceded by one or more qualifiers.
There are three
different kinds of qualifier:
.IP \fIproto\fP
.I proto
qualifiers restrict the match to a particular protocol.
(This should not be confused with the
.B proto
type qualifier below.)
Possible
protocols are:
.BR ether ,
.BR link ,
.BR wlan ,
.BR ip ,
.BR ip6 ,
.BR arp ,
.BR tcp ,
.BR udp ,
.BR sctp ,
.BR iso ,
.BR isis ,
.BR rarp ,
.BR decnet ,
.BR fddi ,
.BR tr ,
.B ppp
and
.BR slip .
E.g., `\fBether src\fP foo', `\fBarp net\fP 128.3', `\fBtcp port\fP 21',
`\fBip proto\fP ospf', `\fBether proto\fP 0x88CC',
`\fBudp portrange\fP 7000-7009', `\fBwlan addr2\fP 0:2:3:4:5:6'.
If there is no
.I proto
qualifier, all protocols consistent with the type are assumed.
E.g., `\fBsrc\fP foo' means `\fB(ip6 or ip or arp or rarp) src\fP foo',
`\fBproto\fP tcp' means `\fB(ip6 or ip) proto\fP tcp'
`\fBnet\fP bar' means `\fB(ip6 or ip or arp or rarp) net\fP bar' and
`\fBport\fP 53' means `\fB(tcp or udp or sctp) port\fP 53'
(note that these examples use invalid syntax to illustrate the principle).
.IP \fIdir\fP
.I dir
qualifiers specify a particular transfer direction to and/or from
.IR id .
Possible directions are
.BR src ,
.BR dst ,
.BR "src or dst" ,
.BR "src and dst" ,
.BR ra ,
.BR ta ,
.BR addr1 ,
.BR addr2 ,
.BR addr3 ,
and
.BR addr4 .
E.g., `\fBsrc\fP foo', `\fBdst net\fP 128.3', `\fBsrc or dst port\fP ftp-data'.
If
there is no dir qualifier, `\fBsrc or dst\fP' is assumed.
The
.BR ra ,
.BR ta ,
.BR addr1 ,
.BR addr2 ,
.BR addr3 ,
and
.B addr4
qualifiers are only valid for IEEE 802.11 Wireless LAN link layers.
.IP \fItype\fP
.I type
qualifiers say what kind of thing the id name or number refers to.
Possible types are
.BR host ,
.BR net ,
.BR \%proto ,
.BR port ,
.BR \%portrange ,
.B \%protochain
and
.B \%gateway.
E.g., `\fBhost\fP foo', `\fBnet\fP 128.3', `\fBport\fP 20', `\fBportrange\fP 6000-6008',
`\fBproto \fP 17'.
If there is no type
qualifier,
.B host
is assumed.
.LP
In primitives that follow this pattern each qualifier kind may be present at
most once, and if more than one kind is present, any
.I proto
qualifier must be the first qualifier and any
.I type
qualifier must be the last qualifier, for example, `\fBtcp dst port\fP 80'.
Also not all combinations of these qualifier kinds are valid syntax.  Some
make no sense in network protocols space, for example:
.B "ether port"
(Ethernet header has no ports),
.B "tcp net"
(TCP header does not have layer 3 fields),
.B "dst proto"
(in a protocol header the same protocol applies to both the source and the
destination), and so on.  Some other combinations are not valid syntax because
they are not implemented, even though hypothetically could make sense, for
example:
.BR "iso net" ,
.B "aarp host"
and so on.
.LP
[\fBfddi\fP is actually an alias for \fBether\fP; the parser treats them
identically as meaning ``the data link level used on the specified
network interface''.  FDDI headers contain Ethernet-like source
and destination addresses, and often contain Ethernet-like packet
types, so you can filter on these FDDI fields just as with the
analogous Ethernet fields.
FDDI headers also contain other fields,
but you cannot name them explicitly in a filter expression.
Similarly, \fBtr\fP and \fBwlan\fP are aliases for \fBether\fP; the
statements about FDDI headers also apply to Token Ring and 802.11 wireless
LAN headers.  The same stands for the
.BR link ,
.B ppp
and
.B slip
keywords.]
.LP
For IEEE 802.11 headers, the destination address is the DA field and the
source address is the SA field.  For both ARP and RARP headers, the
destination address is the TPA (Target Protocol Address) field and the
source address is the SPA (Sender Protocol Address) field.
.LP
In addition to the above, there are some special `primitive' keywords
that don't follow the pattern (for example:
.BR broadcast ,
.BR multicast ,
.BR inbound ,
.BR outbound ,
.BR ifindex ,
.BR llc ,
.BR vlan ,
.BR mpls ,
.BR less ,
.BR greater ),
packet data accessors and relations of two arithmetic expressions.
All of these are described below.
.LP
More complex filter expressions are built up by using the words
.BR and ,
.B or
and
.B not
(or equivalently: `\fB&&\fP', `\fB||\fP' and `\fB!\fP' respectively)
to combine primitives.
E.g., `\fBhost\fP foo \fBand not port\fP ftp \fBand not port\fP ftp-data'.
To save typing, identical qualifier lists can be omitted.
E.g.,
`\fBtcp dst port\fP ftp \fBor\fP ftp-data \fBor\fP domain' is exactly the same as
`\fBtcp dst port\fP ftp \fBor tcp dst port\fP ftp-data \fBor tcp dst port\fP domain'.
.SH PRIMITIVES
.IP "\fBhost \fIhostnameaddr\fR"
True if the source or the destination ARP/IPv4/IPv6/RARP address of the packet is
.IR hostnameaddr .
May be qualified with a specific protocol
.RB ( arp ,
.BR ip ,
.BR ip6 ,
.BR rarp )
and/or a different direction
.RB ( src ,
.BR dst ,
.BR "src and dst" ),
in the latter case the
.B host
keyword is optional.  For example,
.in +.5i
.nf
\fBip src \fIhostnameaddr\fR
.fi
.in -.5i
for Ethernet-like link-layer types is equivalent to
.in +.5i
.nf
\fBether proto \\\fRip \fBand ip src host \fIhostnameaddr\fR
.fi
.in -.5i
.IP
.I hostnameaddr
may be either an address or a name.  If it is a name with multiple IPv4/IPv6 addresses,
each address will be checked for a match.
.IP "\fBether host \fIethernameaddr\fP"
True if the source or the destination Ethernet/802.11/IPFC/ATM LANE/FDDI/Token Ring
address of the packet is
.IR ethernameaddr .
May be qualified with a different direction
.RB ( src ,
.BR dst ,
.BR "src and dst" ),
in which case the
.B host
keyword is optional.
.IP
\fIethernameaddr\fP
may be either a name from /etc/ethers or a numerical MAC address of the
form "xx:xx:xx:xx:xx:xx", "xx.xx.xx.xx.xx.xx", "xx-xx-xx-xx-xx-xx",
"xxxx.xxxx.xxxx" or "xxxxxxxxxxxx",
where each "x" is a hex digit (0-9, a-f, or A-F).
.IP "\fBgateway\fP \fIhost\fP"
True if the packet used \fIhost\fP as a gateway.
I.e., the source or the destination Ethernet-like address is
.I host
but neither the source nor the destination ARP/IPv4/RARP address is
.IR host .
This primitive is valid only for the same link-layer types as the
.B "ether host"
primitive above.  May be qualified with a specific protocol
.RB ( arp ,
.BR ip ,
.BR rarp ).
For example,
.in +.5i
.nf
\fBip gateway \fIhost\fR
.fi
.in -.5i
is equivalent to
.in +.5i
.nf
\fBether host \fIethernameaddr \fBand not ip host \fIhostnameaddr\fR
.fi
.in -.5i
which can be used with either names or numbers for
.I hostnameaddr
and
.IR ethernameaddr .
.IP
\fIHost\fP must be a name and
must be found both by the machine's host-name-to-IP-address resolution
mechanisms (host name file, DNS, NIS, etc.) and by the machine's
host-name-to-Ethernet-address resolution mechanism (/etc/ethers, etc.).
.IP "\fBnet \fInetnameaddr\fR"
True if the source or the destination ARP/IPv4/IPv6/RARP address of the packet
belongs to the specified network.  May be qualified with a specific protocol
.RB ( arp ,
.BR ip ,
.BR ip6 ,
.BR rarp )
and/or a different direction
.RB ( src ,
.BR dst ,
.BR "src and dst" ),
in the latter case the
.B net
keyword remains mandatory.
.I netnameaddr
may be either a name from the networks database
(/etc/networks, etc.) or a network number.
.IP
An IPv4 network number can be written as a dotted quad (e.g., ***********),
dotted triple (e.g., 192.168.1), dotted pair (e.g, 172.16), or single
number (e.g., 10); the netmask is *************** (/32) for a dotted quad
(which means that it's really a host match), ************* (/24) for a dotted
triple, *********** (/16) for a dotted pair, or ********* (/8) for a single number.
An IPv6 network number must be written out fully; the netmask is
ffff:ffff:ffff:ffff:ffff:ffff:ffff:ffff (/128), so in this primitive IPv6
"network" matches are really always host matches.  For an actual IPv6 network
match see the `\fBnet \fInetaddr\fR/\fIlen\fR' primitive below.
.IP "\fBnet \fInetaddr\fR \fBmask \fInetmask\fR"
True if the source or the destination ARP/IPv4/RARP address of the packet
belongs to \fInetaddr\fR with the specified \fInetmask\fR.  May be qualified
with a specific protocol
.RB ( arp ,
.BR ip ,
.BR rarp )
and/or a different direction
.RB ( src ,
.BR dst ,
.BR "src and dst" ),
in the latter case the
.B net
keyword remains mandatory.  In this implementation this primitive does not
support IPv6 networks.
.IP
Both
.I netaddr
and
.I netmask
use the IPv4 network number notation described above, except the "single
number" form is not valid in this primitive.  For example,
.in +.5i
.nf
\fBnet \fP192.168 \fBmask \fP255.255\fR
.fi
.in -.5i
is equivalent to
.in +.5i
.nf
\fBnet \fP192.168.0.0 \fBmask \fP***********\fR
.fi
.in -.5i
.IP
The
.I netmask
can represent any 32-bit value, which is why the
`\fBnet \fI\%netaddr\fR/\fIlen\fR' primitive below is usually a better fit for
use cases that require the value to be strictly one of the 33 CIDR masks (from
/0 to /32).
.IP "\fBnet \fInetaddr\fR/\fIlen\fR"
True if the source or the destination ARP/IPv4/IPv6/RARP address of the packet
belongs to
.I netaddr
where the bit-length of the network mask equals
.I len
(in other words, the address belongs to the specified CIDR prefix).  May be
qualified with a specific protocol
.RB ( arp ,
.BR ip ,
.BR ip6 ,
.BR rarp )
and/or a different direction
.RB ( src ,
.BR dst ,
.BR "src and dst" ),
in the latter case the
.B net
keyword is optional.
.IP
For IPv4,
.I len
is an integer between 0 and 32 (both inclusive) and
.I netaddr
is the same as the above.  For IPv6,
.I len
is an integer between 0 and 128 (both inclusive) and
.I netaddr
is an IPv6 address.  For the latter zero compression notation
.RB ( :: )
is valid, but IPv4-mapped notation
.RB ( x:x:x:x:x:x:d.d.d.d )
is not.  For both IPv4 and IPv6 the maximum value of
.I len
is equivalent to a host match and the 0 value (which implies an all-zeroes
value of
.IR netaddr )
matches any address.  In the latter case this primitive reduces to matching
the specified (or implied) protocols only.
.IP "\fBport \fIportnamenum\fR"
True if the source or the destination TCP/UDP/SCTP port of an IPv4/IPv6 packet
is
.IR portnamenum .
For IPv4 this also implies that the packet is the first fragment or is not
fragmented.  May be qualified with a specific layer 4 protocol
.RB ( tcp ,
.BR udp ,
.BR sctp )
or a different direction
.RB ( src ,
.BR dst ,
.BR "src and dst" ),
in the latter case the
.B port
keyword remains mandatory.  Cannot be qualified with a specific layer 3
protocol (IPv4/IPv6) in the same primitive, but can be trivially combined with
other primitives to achieve the required effect, for example:
.in +.5i
.nf
\fBip and tcp dst port \fP80\fR
.fi
.in -.5i
.IP
The \fIportnamenum\fP can be a number or a name used in /etc/services (see
.BR services (5)).
If a name is used, both the port
number and protocol are checked.
If a number or ambiguous name is used,
only the port number is checked (e.g., `\fBdst port\fR 513' will print both
tcp/login traffic and udp/who traffic, and `\fBport\fR domain' will print
both tcp/domain and udp/domain traffic).
.IP "\fBportrange \fIportnamenum1-portnamenum2\fR"
This is a more generic form of the above: true if the port number in the
packet is between
.I portnamenum1
and
.I portnamenum2
(both inclusive), everything else holds the same meaning.
In this predicate
.I portnamenum1
and
.I portnamenum2
can be specified in either order.  If the two values are equal, this primitive
has the same effect as the
.B port
primitive above.
.IP "\fBless \fIlength\fR"
True if the packet has a length less than or equal to \fIlength\fP.
This is equivalent to:
.in +.5i
.nf
\fBlen <= \fIlength\fP
.fi
.in -.5i
.IP "\fBgreater \fIlength\fR"
True if the packet has a length greater than or equal to \fIlength\fP.
This is equivalent to:
.in +.5i
.nf
\fBlen >= \fIlength\fP
.fi
.in -.5i
.IP "\fBip proto \fIprotocol\fR"
True if the packet is an IPv4 packet of protocol type \fIprotocol\fP.
\fIProtocol\fP can be a number or one of the names recognized by
.BR getprotobyname (3),
for example:
.BR ah ,
.BR esp ,
.B eigrp
(only in Linux with glibc, FreeBSD, NetBSD, DragonFly BSD, and macOS),
.BR icmp ,
.BR igmp ,
.B igrp
(only in Haiku and OpenBSD),
.BR pim ,
.BR sctp ,
.BR tcp ,
.B udp
or
.BR vrrp .
Note that most of these example identifiers
are also keywords and must be escaped via backslash (\\).
Note that this primitive does not chase the protocol header chain.
.IP
Typically
.BR getprotobyname (3)
parses the
.IR \%/etc/protocols
file to translate protocol names to numbers, and the
.B "getent protocols"
command lists the protocols recognised by the function.  This is not entirely
so in AIX (which does not have the command), Haiku (which has the file at
.IR \%/system/data/network/protocols
and does not have the command), on Linux with musl libc (which hard-codes the
list of protocols) and on hosts that use a network database to resolve
protocol names to numbers (see
.BR nsswitch.conf (5)).
If a protocol name fails to translate to a number, this version of libpcap
will treat the filter expression as invalid.
.IP "\fBcarp\fR, \fBvrrp\fR"
Abbreviations for:
.in +.5i
.nf
\fBip proto\fR 112
.fi
.in -.5i
.IP "\fBicmp\fR"
Abbreviation for:
.in +.5i
.nf
\fBip proto\fR 1
.fi
.in -.5i
.IP "\fBigmp\fR"
Abbreviation for:
.in +.5i
.nf
\fBip proto\fR 2
.fi
.in -.5i
.IP "\fBigrp\fR"
Abbreviation for:
.in +.5i
.nf
\fBip proto \fIigrpval\fR
.fi
.in -.5i
where
.I igrpval
is 88 on DragonFly BSD, FreeBSD and macOS, and 9 on all other OSes.  This
abbreviation should not be used in portable applications and may be removed
in future.
.IP "\fBip6 proto \fIprotocol\fR"
True if the packet is an IPv6 packet of protocol type \fIprotocol\fP.
(See `\fBip proto\fP' above for the meaning of \fIprotocol\fR.)
Note that the IPv6 variant of ICMP uses a different protocol number, named
.B \%ipv6-icmp
in AIX, FreeBSD, illumos, Haiku, GNU/Hurd, Linux, macOS, NetBSD, OpenBSD,
Solaris and Windows.
Note that this primitive does not chase the protocol header chain.
.IP "\fBicmp6\fR"
Abbreviation for:
.in +.5i
.nf
\fBip6 proto\fR 58
.fi
.in -.5i
.IP "\fBproto \fIprotocol\fR"
True if the packet is an IPv4 or IPv6 packet of protocol type
\fIprotocol\fP.  (See `\fBip proto\fP' above for the meaning of
\fIprotocol\fP.)  Note that this primitive does not chase the protocol
header chain.
.IP  "\fBah\fR, \fBesp\fR, \fBpim\fR, \fBsctp\fR, \fBtcp\fR, \fBudp\fR"
Abbreviations for:
.in +.5i
.nf
\fBproto \\\fIprotocol\fR
.fi
.in -.5i
where \fIprotocol\fR is one of the above protocols.
.IP "\fBip6 protochain \fIprotocol\fR"
True if the packet is IPv6 packet,
and contains protocol header with type \fIprotocol\fR
in its protocol header chain.
(See `\fBip proto\fP' above for the meaning of \fIprotocol\fP.)
For example,
.in +.5i
.nf
\fBip6 protochain\fR 6
.fi
.in -.5i
matches any IPv6 packet with TCP protocol header in the protocol header chain.
The packet may contain, for example,
authentication header, routing header, or hop-by-hop option header,
between IPv6 header and TCP header.
The BPF code emitted by this primitive is complex and
cannot be optimized by the BPF optimizer code, and is not supported by
filter engines in the kernel, so this can be somewhat slow, and may
cause more packets to be dropped.
.IP "\fBip protochain \fIprotocol\fR"
Equivalent to \fBip6 protochain \fIprotocol\fR, but this is for IPv4.
(See `\fBip proto\fP' above for the meaning of \fIprotocol\fP.)
.IP "\fBprotochain \fIprotocol\fR"
True if the packet is an IPv4 or IPv6 packet of protocol type
\fIprotocol\fP.  (See `\fBip proto\fP' above for the meaning of
\fIprotocol\fP.)  Note that this primitive chases the protocol
header chain.
.IP "\fBether broadcast\fR"
True if the destination Ethernet/802.11/IPFC/ARCnet/ATM LANE/FDDI/Token Ring address of
the packet is the broadcast address (e.g.
.B FF:FF:FF:FF:FF:FF
for Ethernet).  The
.B ether
keyword is optional.
.IP "\fBip broadcast\fR"
True if the packet is an IPv4 packet with the host part of the destination
address being either all-ones or all-zeroes.  This primitive requires to
specify the netmask, which cannot be done in the filter expression; the only
way to specify a netmask is via the
.B \%netmask
argument of the
.BR \%pcap_compile ()
function.  If a netmask has not been specified, an attempt to compile a
filter expression with this primitive will return an error.
.IP
Note that this primitive ignores the network part of the destination address,
thus it can match more packets than expected, especially if the interface has
multiple IPv4 addresses with different netmasks.  For example, if the
interface has addresses **********/29 and ***************/24 configured and
the
.B \%netmask
argument corresponds to the first address, its value will be
.BR \%0xFFFFFFF8
and the host mask value will be
.BR \%0x00000007 .
This will match the expected two addresses in the first prefix (********* and
**********), as well as 64 addresses in the second prefix (*************,
*************, *************, **************, ************** and so on), as
well as any other IPv4 address with the lowest 3 bits being all-ones or
all-zeroes (for example: ************, *************, ************) -- in
other words, 25% of the complete IPv4 address space.  This is why in use
cases that require more precision it would be better to match the required
address(es) explicitly, for example:
.in +.5i
.nf
\fBip dst host ********* or **********\fR
.fi
.in -.5i
.IP "\fBether multicast\fR"
True if the destination Ethernet/802.11/IPFC/ARCnet/ATM LANE/FDDI/Token Ring address of
the packet is a multicast address (e.g.
.B "\%ether[0] & 1 != 0"
for Ethernet).  The
.B ether
keyword is optional.
.IP "\fBip multicast\fR"
True if the packet is an IPv4 multicast packet.
.IP "\fBip6 multicast\fR"
True if the packet is an IPv6 multicast packet.
.IP  "\fBether proto \fIprotocol\fR"
True if the packet is of ether type \fIprotocol\fR.
\fIProtocol\fP can be a number or one of the names
\fBaarp\fP, \fBarp\fP, \fBatalk\fP, \fBdecnet\fP, \fBip\fP, \fBip6\fP,
\fBipx\fP, \fBiso\fP, \fBlat\fP, \fBloopback\fP, \fBmopdl\fP, \fBmoprc\fP, \fBnetbeui\fP,
\fBrarp\fP, \fBsca\fP or \fBstp\fP.
Note these identifiers (except \fBloopback\fP) are also keywords
and must be escaped via backslash (\\).
.IP
[In the case of FDDI (e.g., `\fBfddi proto \\arp\fR'), Token Ring
(e.g., `\fBtr proto \\arp\fR'), and IEEE 802.11 wireless LANs (e.g.,
`\fBwlan proto \\arp\fR'), for most of those protocols, the
protocol identification comes from the 802.2 Logical Link Control (LLC)
header, which is usually layered on top of the FDDI, Token Ring, or
802.11 header.
.IP
When filtering for most protocol identifiers on FDDI, Token Ring, or
802.11, the filter checks only the protocol ID field of an LLC header
in so-called SNAP format with an Organizational Unit Identifier (OUI) of
0x000000, for encapsulated Ethernet; it doesn't check whether the packet
is in SNAP format with an OUI of 0x000000.
The exceptions are:
.RS
.TP
\fBiso\fP
the filter checks the DSAP (Destination Service Access Point) and
SSAP (Source Service Access Point) fields of the LLC header;
.TP
\fBstp\fP and \fBnetbeui\fP
the filter checks the DSAP of the LLC header;
.TP
\fBatalk\fP
the filter checks for a SNAP-format packet with an OUI of 0x080007
and the AppleTalk etype.
.RE
.IP
In the case of Ethernet, the filter checks the Ethernet type field
for most of those protocols.  The exceptions are:
.RS
.TP
\fBiso\fP, \fBstp\fP, and \fBnetbeui\fP
the filter checks for an 802.3 frame and then checks the LLC header as
it does for FDDI, Token Ring, and 802.11;
.TP
\fBatalk\fP
the filter checks both for the AppleTalk etype in an Ethernet frame and
for a SNAP-format packet as it does for FDDI, Token Ring, and 802.11;
.TP
\fBaarp\fP
the filter checks for the AppleTalk ARP etype in either an Ethernet
frame or an 802.2 SNAP frame with an OUI of 0x000000;
.TP
\fBipx\fP
the filter checks for the IPX etype in an Ethernet frame, the IPX
DSAP in the LLC header, the 802.3-with-no-LLC-header encapsulation of
IPX, and the IPX etype in a SNAP frame.
.RE
.IP "\fBip\fR, \fBip6\fR, \fBarp\fR, \fBrarp\fR, \fBatalk\fR, \fBaarp\fR, \fBdecnet\fR, \fBiso\fR, \fBstp\fR, \fBipx\fR, \fBnetbeui\fP"
Abbreviations for:
.in +.5i
.nf
\fBether proto \\\fIprotocol\fR
.fi
.in -.5i
where \fIprotocol\fR is one of the above protocols.
.IP "\fBlat\fR, \fBmopdl\fR, \fBmoprc\fR, \fBsca\fR"
Abbreviations for:
.in +.5i
.nf
\fBether proto \\\fIprotocol\fR
.fi
.in -.5i
where \fIprotocol\fR is one of the above protocols, all of which originated
at DEC, but are not the same as DECnet.  Namely,
.B lat
is Local Area Transport (LAT),
.B mopdl
is DNA Dump/Load (MOP),
.B moprc
is DNA Remote Console (MOP) and
.B sca
is System Communication Architecture (SCA).
.IP "\fBdecnet host \fIdecnetaddr\fR"
True if the source or the destination DECnet address of the packet is
.IR decnetaddr .
May be qualified with a different direction
.RB ( src ,
.BR dst ,
.BR "src and dst" ),
in which case the
.B host
keyword is optional.
.IP
.I decnetaddr
is an address of the form
.BR AREANUMBER.NODENUMBER ,
where the area number can be between 0 and 63 (both inclusive) and the node
number can be between 0 and 1023 (both inclusive) and both numbers always use
decimal base.  For example:
.in +.5i
.nf
\fBdecnet src \fP10.123\fR
.fi
.in -.5i
.IP \fBllc\fP
True if the packet has an 802.2 LLC header.  This includes:
.IP
Ethernet packets with a length field rather than a type field that
aren't raw NetWare-over-802.3 packets;
.IP
IEEE 802.11 data packets;
.IP
Token Ring packets (no check is done for LLC frames);
.IP
FDDI packets (no check is done for LLC frames);
.IP
LLC-encapsulated ATM packets, for SunATM on Solaris.
.IP "\fBllc\fP \fItype\fR"
True if the packet has an 802.2 LLC header and has the specified
.IR type .
.I type
can be one of:
.RS
.TP
\fBi\fR
Information (I) PDUs
.TP
\fBs\fR
Supervisory (S) PDUs
.TP
\fBu\fR
Unnumbered (U) PDUs
.TP
\fBrr\fR
Receiver Ready (RR) S PDUs
.TP
\fBrnr\fR
Receiver Not Ready (RNR) S PDUs
.TP
\fBrej\fR
Reject (REJ) S PDUs
.TP
\fBui\fR
Unnumbered Information (UI) U PDUs
.TP
\fBua\fR
Unnumbered Acknowledgment (UA) U PDUs
.TP
\fBdisc\fR
Disconnect (DISC) U PDUs
.TP
\fBdm\fR
Disconnected Mode (DM) U PDUs
.TP
\fBsabme\fR
Set Asynchronous Balanced Mode Extended (SABME) U PDUs
.TP
\fBtest\fR
Test (TEST) U PDUs
.TP
\fBxid\fR
Exchange Identification (XID) U PDUs
.TP
\fBfrmr\fR
Frame Reject (FRMR) U PDUs
.RE
.IP \fBinbound\fP
Packet was received by the host performing the capture rather than being
sent by that host.  This is only supported for certain link-layer types,
such as SLIP and the ``cooked'' Linux capture mode
used for the ``any'' device and for some other device types.
.IP \fBoutbound\fP
Packet was sent by the host performing the capture rather than being
received by that host.  This is only supported for certain link-layer types,
such as SLIP and the ``cooked'' Linux capture mode
used for the ``any'' device and for some other device types.
.IP "\fBifindex \fIinterface_index\fR"
True if the packet was logged via the specified interface (applies only to
packets logged by the Linux "any" cooked v2 interface).
.IP "\fBifname \fIinterface\fR"
True, for
.BR DLT_PFLOG
only, if the packet was logged as coming from the specified interface.
.IP "\fBon \fIinterface\fR"
Synonymous with the
.B ifname
primitive.
.IP "\fBrnr \fInum\fR"
True, for
.BR DLT_PFLOG
only, if the packet was logged as matching the specified PF rule number.
.IP "\fBrulenum \fInum\fR"
Synonymous with the
.B rnr
primitive.
.IP "\fBreason \fIcode\fR"
True, for
.BR DLT_PFLOG
only, if the packet was logged with the specified PF reason code.  Valid
codes are:
.BR \%match ,
.BR \%bad-offset ,
.BR \%fragment ,
.BR \%short ,
.BR \%normalize ,
.BR \%memory ,
.BR \%bad-timestamp ,
.BR \%congestion ,
.BR \%ip-option ,
.BR \%proto-cksum ,
.BR \%state-mismatch ,
.BR \%state-insert ,
.BR \%state-limit ,
.BR \%src-limit ,
.BR \%synproxy ,
.B \%map-failed
(on FreeBSD only),
.B \%state-locked
(on NetBSD only),
.B \%translate
(on OpenBSD only),
.B \%no-route
(on OpenBSD only) and
.B \%dummynet
(on macOS only).
.IP "\fBrset \fIname\fR"
True, for
.BR DLT_PFLOG
only, if the packet was logged as matching the specified PF ruleset
name of an anchored ruleset.
.IP "\fBruleset \fIname\fR"
Synonymous with the
.B rset
primitive.
.IP "\fBsrnr \fInum\fR"
True, for
.BR DLT_PFLOG
only, if the packet was logged as matching the specified PF rule number
of an anchored ruleset.
.IP "\fBsubrulenum \fInum\fR"
Synonymous with the
.B srnr
primitive.
.IP "\fBaction \fIact\fR"
True, for
.BR DLT_PFLOG
only, if PF took the specified action when the packet was logged.  Valid
actions are:
.B pass
(or
.BR \%accept ),
.B block
(or
.BR drop )
and, with later versions of
.BR pf (4),
.BR scrub ,
.BR \%noscrub ,
.BR nat ,
.BR \%nonat ,
.BR \%binat ,
.BR \%nobinat ,
.BR rdr ,
.BR nordr ,
.BR \%synproxy-drop ,
.B \%defer
(on FreeBSD and OpenBSD only),
.B match
(on OpenBSD only),
.B \%divert
(on OpenBSD only),
.B rt
(on OpenBSD only),
.B afrt
(on OpenBSD only),
.B \%dummynet
(on macOS only),
.B \%nodummynet
(on macOS only),
.B nat64
(on macOS only) and
.B \%nonat64
(on macOS only).
.IP "\fBwlan ra \fIehost\fR"
True if the IEEE 802.11 RA is
.IR ehost .
The RA field is used in all frames except for management frames.
.IP "\fBwlan ta \fIehost\fR"
True if the IEEE 802.11 TA is
.IR ehost .
The TA field is used in all frames except for management frames and
CTS (Clear To Send) and ACK (Acknowledgment) control frames.
.IP "\fBwlan addr1 \fIehost\fR"
True if the first IEEE 802.11 address is
.IR ehost .
.IP "\fBwlan addr2 \fIehost\fR"
True if the second IEEE 802.11 address, if present, is
.IR ehost .
The second address field is used in all frames except for CTS (Clear To
Send) and ACK (Acknowledgment) control frames.
.IP "\fBwlan addr3 \fIehost\fR"
True if the third IEEE 802.11 address, if present, is
.IR ehost .
The third address field is used in management and data frames, but not
in control frames.
.IP "\fBwlan addr4 \fIehost\fR"
True if the fourth IEEE 802.11 address, if present, is
.IR ehost .
The fourth address field is only used for
WDS (Wireless Distribution System) frames.
.IP "\fBwlan type \fIwlan_type\fR"
True if the IEEE 802.11 frame type matches the specified \fIwlan_type\fR.
Valid \fIwlan_type\fRs are:
\fBmgt\fP,
\fBctl\fP
and \fBdata\fP.
The
.B wlan
keyword is optional.
.IP "\fBwlan type \fIwlan_type \fBsubtype \fIwlan_subtype\fR"
True if the IEEE 802.11 frame type matches the specified \fIwlan_type\fR
and frame subtype matches the specified \fIwlan_subtype\fR.
The
.B wlan
keyword is optional.
.IP
If the specified \fIwlan_type\fR is \fBmgt\fP,
then valid \fIwlan_subtype\fRs are:
\fBassoc-req\fP,
\fBassoc-resp\fP,
\fBreassoc-req\fP,
\fBreassoc-resp\fP,
\fBprobe-req\fP,
\fBprobe-resp\fP,
\fBbeacon\fP,
\fBatim\fP,
\fBdisassoc\fP,
\fBauth\fP and
\fBdeauth\fP.
.IP
If the specified \fIwlan_type\fR is \fBctl\fP,
then valid \fIwlan_subtype\fRs are:
\fBbar\fP,
\fBba\fP,
\fBps-poll\fP,
\fBrts\fP,
\fBcts\fP,
\fBack\fP,
\fBcf-end\fP and
\fBcf-end-ack\fP.
.IP
If the specified \fIwlan_type\fR is \fBdata\fP,
then valid \fIwlan_subtype\fRs are:
.BR \%data ,
.BR \%data-cf-ack ,
.BR \%data-cf-poll ,
.BR \%data-cf-ack-poll ,
.BR \%null ,
.BR \%cf-ack ,
.BR \%cf-poll ,
.BR \%cf-ack-poll ,
.BR \%qos-data ,
.BR \%qos-data-cf-ack ,
.BR \%qos-data-cf-poll ,
.BR \%qos-data-cf-ack-poll ,
.BR \%qos ,
.B \%qos-cf-poll
and
.BR \%qos-cf-ack-poll .
.IP "\fBwlan subtype \fIwlan_subtype\fR"
True if the IEEE 802.11 frame subtype matches the specified \fIwlan_subtype\fR
and frame has the type to which the specified \fIwlan_subtype\fR belongs.
The
.B wlan
keyword is optional.
.IP "\fBwlan dir \fIdirection\fR"
True if the IEEE 802.11 frame direction matches the specified
.IR direction .
Valid directions are:
.BR nods ,
.BR tods ,
.BR fromds ,
.BR dstods ,
or a numeric value.
The
.B wlan
keyword is optional.
.IP "\fBvlan \fI[vlan_id]\fR"
True if the packet is an IEEE 802.1Q VLAN packet.
If the optional \fIvlan_id\fR is specified, only true if the packet has the specified
\fIvlan_id\fR.
Note that the first \fBvlan\fR keyword encountered in an expression
changes the decoding offsets for the remainder of the expression on
the assumption that the packet is a VLAN packet.  The `\fBvlan
\fI[vlan_id]\fR` keyword may be used more than once, to filter on VLAN
hierarchies.  Each use of that keyword increments the filter offsets
by 4.
.IP
For example:
.in +.5i
.nf
\fBvlan\fP 100 \fB&& vlan\fR 200
.fi
.in -.5i
filters on VLAN 200 encapsulated within VLAN 100, and
.in +.5i
.nf
\fBvlan && vlan \fP300 \fB&& ip\fR
.fi
.in -.5i
filters IPv4 protocol encapsulated in VLAN 300 encapsulated within any
higher order VLAN.
.IP "\fBmpls \fI[label_num]\fR"
True if the packet is an MPLS packet.
If the optional \fIlabel_num\fR is specified, only true if the packet has the specified
\fIlabel_num\fR.
Note that the first \fBmpls\fR keyword encountered in an expression
changes the decoding offsets for the remainder of the expression on
the assumption that the packet is a MPLS-encapsulated IP packet.  The
`\fBmpls \fI[label_num]\fR` keyword may be used more than once, to
filter on MPLS hierarchies.  Each use of that keyword increments the
filter offsets by 4.
.IP
For example:
.in +.5i
.nf
\fBmpls\fP 100000 \fB&& mpls\fR 1024
.fi
.in -.5i
filters packets with an outer label of 100000 and an inner label of
1024, and
.in +.5i
.nf
\fBmpls && mpls\fP 1024 \fB&& host\fR ***********
.fi
.in -.5i
filters packets to or from *********** with an inner label of 1024 and
any outer label.
.IP \fBpppoed\fP
True if the packet is a PPP-over-Ethernet Discovery packet (Ethernet
type 0x8863).
.IP "\fBpppoes \fI[session_id]\fR"
True if the packet is a PPP-over-Ethernet Session packet (Ethernet
type 0x8864).
If the optional \fIsession_id\fR is specified, only true if the packet has the specified
\fIsession_id\fR.
Note that the first \fBpppoes\fR keyword encountered in an expression
changes the decoding offsets for the remainder of the expression on
the assumption that the packet is a PPPoE session packet.
.IP
For example:
.in +.5i
.nf
\fBpppoes\fP 0x27 \fB&& ip\fR
.fi
.in -.5i
filters IPv4 protocol encapsulated in PPPoE session id 0x27.
.IP "\fBgeneve \fI[vni]\fR"
True if the packet is a Geneve packet (UDP port 6081). If the optional \fIvni\fR
is specified, only true if the packet has the specified \fIvni\fR.
Note that when the \fBgeneve\fR keyword is encountered in
an expression, it changes the decoding offsets for the remainder of
the expression on the assumption that the packet is a Geneve packet.
.IP
For example:
.in +.5i
.nf
\fBgeneve\fP 0xb \fB&& ip\fR
.fi
.in -.5i
filters IPv4 protocol encapsulated in Geneve with VNI 0xb. This will
match both IPv4 directly encapsulated in Geneve as well as IPv4 contained
inside an Ethernet frame.
.IP "\fBvxlan \fI[vni]\fR"
True if the packet is a VXLAN packet (UDP port 4789). If the optional
\fIvni\fR is specified, only true if the packet has the specified
\fIvni\fR.  Note that when the \fBvxlan\fR keyword is encountered in
an expression, it changes the decoding offsets for the remainder of
the expression on the assumption that the packet is a VXLAN packet.
.IP
For example:
.in +.5i
.nf
\fBvxlan\fP 0x7 \fB&& ip6 \fR
.fi
.in -.5i
filters IPv6 protocol encapsulated in VXLAN with VNI 0x7.
.IP "\fBiso proto \fIprotocol\fR"
True if the packet is an OSI packet of protocol type \fIprotocol\fP.
\fIProtocol\fP can be a number or one of the names
\fBclnp\fP, \fBesis\fP, or \fBisis\fP.
.IP "\fBclnp\fR, \fBesis\fR, \fBisis\fR"
Abbreviations for:
.in +.5i
.nf
\fBiso proto \\\fIprotocol\fR
.fi
.in -.5i
where \fIprotocol\fR is one of the above protocols.  Also in this context
.B \%es-is
is an alias for
.B esis
and
.B \%is-is
is an alias for
.BR isis .
.IP "\fBisis proto \fIprotocol\fR"
True if the packet is an IS-IS packet of protocol type
.IR protocol ,
which can be a number only.
.IP "\fBl1\fR, \fBl2\fR, \fBiih\fR, \fBlsp\fR, \fBsnp\fR, \fBcsnp\fR, \fBpsnp\fR"
Abbreviations for IS-IS PDU types.
.IP "\fIatmfield relop val\fR"
True if the packet is an ATM packet, for SunATM on Solaris, and the relation
holds.
.I atmfield
is one of
.RB { vpi ,
.BR vci };
.I relop
is one of
.RB { > ,
.BR < ,
.BR >= ,
.BR <= ,
.BR = ,
.BR == ,
.BR != }
(where
.B =
means the same as
.BR == );
.I val
is an integer.
.B vpi
and
.B vci
stand for the virtual path identifier (VPI) and the virtual channel
identifier (VCI) fields respectively.
.IP "\fIatmfield\fP \fIval\fR"
Abbreviation for
.in +.5i
.nf
\fIatmfield\fR == \fIval\fR
.fi
.in -.5i
in the expression above.
.IP "\fIatmfield\fP (\fIval1\fR \fBor\fP ... \fBor\fP \fIvalN\fR)"
Abbreviation for
.in +.5i
.nf
(\fIatmfield\fP == \fIval1\fP \fBor\fP ... \fBor\fP \fIatmfield\fP == \fIvalN\fR)
.fi
.in -.5i
in the expression above.
.IP \fBlane\fP
True if the packet is an ATM packet, for SunATM on Solaris, and is
an ATM LANE packet.
Note that the first \fBlane\fR keyword encountered in an expression
changes the tests done in the remainder of the expression
on the assumption that the packet is either a LANE emulated Ethernet
packet or a LANE LE Control packet.  If \fBlane\fR isn't specified, the
tests are done under the assumption that the packet is an
LLC-encapsulated packet.
.IP
Also the first
.B lane
keyword enables primitives that do not apply to ATM in general, such as
.B "link host"
and
.BR "link multicast" .
.IP \fBoamf4sc\fP
True if the packet is an ATM packet, for SunATM on Solaris, and is
a segment OAM F4 flow cell (VPI=0 & VCI=3).
.IP \fBoamf4ec\fP
True if the packet is an ATM packet, for SunATM on Solaris, and is
an end-to-end OAM F4 flow cell (VPI=0 & VCI=4).
.IP \fBoamf4\fP
True if the packet is an ATM packet, for SunATM on Solaris, and is
a segment or end-to-end OAM F4 flow cell (VPI=0 & (VCI=3 | VCI=4)).
.IP \fBoam\fP
True if the packet is an ATM packet, for SunATM on Solaris, and is
a segment or end-to-end OAM F4 flow cell (VPI=0 & (VCI=3 | VCI=4)).
.IP \fBmetac\fP
True if the packet is an ATM packet, for SunATM on Solaris, and is
on a meta signaling circuit (VPI=0 & VCI=1).
.IP \fBbcc\fP
True if the packet is an ATM packet, for SunATM on Solaris, and is
on a broadcast signaling circuit (VPI=0 & VCI=2).
.IP \fBsc\fP
True if the packet is an ATM packet, for SunATM on Solaris, and is
on a signaling circuit (VPI=0 & VCI=5).
.IP \fBilmic\fP
True if the packet is an ATM packet, for SunATM on Solaris, and is
on an ILMI circuit (VPI=0 & VCI=16).
.IP \fBconnectmsg\fP
True if the packet is an ATM packet, for SunATM on Solaris, and is
on a signaling circuit and is a Q.2931 Setup, Call Proceeding, Connect,
Connect Ack, Release, or Release Done message.
.IP \fBmetaconnect\fP
True if the packet is an ATM packet, for SunATM on Solaris, and is
on a meta signaling circuit and is a Q.2931 Setup, Call Proceeding, Connect,
Release, or Release Done message.
.IP \fBfisu\fP
True if the packet is a Fill-In Signal Unit (FISU) MTP2 packet.
.IP \fBlssu\fP
True if the packet is a Link Status Signal Unit (LSSU) MTP2 packet.
.IP \fBmsu\fP
True if the packet is a Message Signal Unit (MSU) MTP2 packet.
.IP "\fImtpfield relop val\fR"
True if the relation holds.
.I mtpfield
is one of
.RB { sio ,
.BR dpc ,
.BR opc ,
.BR sls };
.I relop
is one of
.RB { > ,
.BR < ,
.BR >= ,
.BR <= ,
.BR = ,
.BR == ,
.BR != }
(where
.B =
means the same as
.BR == );
.I val
is an integer.
.B sio
stands for the Service Information Octet (SIO) field of the MTP2 MSU header.
.BR dpc ,
.B opc
and
.B sls
stand for the Destination Point Code (DPC), Originating Point Code (OPC) and
Signalling Link Selection (SLS) fields respectively of the MTP3 standard
routing label.
.IP "\fImtpfield\fP \fIval\fR"
Abbreviation for
.in +.5i
.nf
\fImtpfield\fR == \fIval\fR
.fi
.in -.5i
in the expression above.
.IP "\fImtpfield\fP (\fIval1\fR \fBor\fP ... \fBor\fP \fIvalN\fR)"
Abbreviation for
.in +.5i
.nf
(\fImtpfield\fP == \fIval1\fP \fBor\fP ... \fBor\fP \fImtpfield\fP == \fIvalN\fR)
.fi
.in -.5i
in the expression above.
.IP "\fBhfisu\fR, \fBhlssu\fR, \fBhmsu\fR, \fBhsio\fR, \fBhdpc\fR, \fBhopc\fR, \fBhsls\fP"
Same as
.BR fisu ,
.BR lssu ,
.BR msu ,
.BR sio ,
.BR dpc ,
.B opc
and
.B sls
respectively, but only if the MTP2 link uses the extended sequence numbers
encoding specified for high speed signalling links (HSL) in ITU-T
Recommendation Q.703 Annex A.
.IP "\fBlink host \fIarcnetaddr\fR"
True, only for
.B \%DLT_ARCNET
or
.BR \%DLT_ARCNET_LINUX ,
if the source or the destination ARCnet address of the packet is
.IR \%arcnetaddr .
May be qualified with a different direction
.RB ( src ,
.BR dst ,
.BR "src and dst" ),
in which case the
.B host
keyword is optional.
.IP
.I \%arcnetaddr
is a string of the form
.B $xx
or
.BR $x ,
where "x" is a hexadecimal digit.  For example:
.in +.5i
.nf
\fBlink host \fP$2b\fR
.fi
.in -.5i
.IP
Also in ARCnet context
.B \%broadcast
and
.B \%multicast
are equivalent to
.BR "\%link dst $0" .
.IP
Note that this address syntax clashes with the parameter expansion syntax
in POSIX-compatible shells and elsewhere, so depending on the use case the
filter string may require the use of single quotes or a backslash.
.IP "\fBbyte \fIidx op val\fR"
True if the value of the link layer byte number
.I idx
satisfies a condition with regard to
.IR val ,
which can be a number only.  The condition is one of: "equals to" (if
.I op
is
.BR = ),
"less than" (if
.I op
is
.BR < ),
"greater than" (if
.I op
is
.BR > ),
"the result of bitwise AND is not zero" (if
.I op
is
.BR & ),
"the result of bitwise OR is not zero" (if
.I op
is
.BR | ).
.IP
The arithmetic expressions and packet data accessors below implement all of
these and many other things much better, so this primitive will be removed in
a future release and should not be used in applications that require forward
compatibility.
.SH ARITHMETIC EXPRESSIONS
Arithmetic expressions are the operands of a relational operator in a
relation of the following form:
.in +.5i
.nf
\fIexpr1 relop expr2\fR
.fi
.in -.5i
This evaluates to true if and only if the relation holds.
.I relop
(the relational operator) is one of
.RB { > ,
.BR < ,
.BR >= ,
.BR <= ,
.BR = ,
.BR == ,
.BR != }
(where
.B =
means the same as
.BR == ).
Each of \fIexpr1\fR and \fIexpr2\fR is an arithmetic expression composed of
integer constants (expressed in standard C syntax), the common arithmetic and
bitwise binary operations
.RB { + ,
.BR - ,
.BR * ,
.BR / ,
.BR % ,
.BR & ,
.BR | ,
.BR ^ ,
.BR << ,
.BR >> },
a length operator, and packet data accessors.  All arithmetic expressions
regardless of the complexity and composition resolve to an integer value.
Note that all comparisons are unsigned, so that, for example, both 0x80000000
and 0xffffffff are > 0.
.LP
The
.B %
and
.B ^
operators are currently only supported for filtering in the kernel on
particular operating systems (for example: FreeBSD, Linux with 3.7 and later
kernels, NetBSD); on all other systems (for example: AIX, Hurd, illumos, Solaris,
OpenBSD), if
those operators are used, filtering will be done in user mode, which
will increase the overhead of capturing packets and may cause more
packets to be dropped.
.LP
The length operator, indicated by the keyword \fBlen\fP, gives the
length of the packet.
.SH PACKET DATA ACCESSORS
To use the packet data in an arithmetic expression, use the following syntax:
.in +.5i
.nf
\fIproto\fB [ \fIexpr\fB : \fIsize\fB ]\fR
.fi
.in -.5i
.I Proto
is one of
.BR arp ,
.BR atalk ,
.BR carp ,
.BR decnet ,
.BR ether ,
.BR fddi ,
.BR icmp ,
.BR icmp6 ,
.BR igmp ,
.BR igrp ,
.BR ip ,
.BR ip6 ,
.BR lat ,
.BR link ,
.BR mopdl ,
.BR moprc ,
.BR pim ,
.BR ppp ,
.BR radio ,
.BR rarp ,
.BR sca ,
.BR sctp ,
.BR slip ,
.BR tcp ,
.BR tr ,
.BR udp ,
.B vrrp
or
.BR wlan ,
and
indicates the protocol layer for the index operation.
.RB ( ether ,
.BR fddi ,
.BR link ,
.BR ppp ,
.BR slip ,
.B tr
and
.BR wlan
all refer to the
link layer, \fBradio\fR refers to the "radio header" added to some
802.11 captures.)
Note that \fBtcp\fR, \fBudp\fR and other upper-layer protocol types only
apply to IPv4, not IPv6 (this will be fixed in the future).
The byte offset, relative to the indicated protocol layer, is
given by \fIexpr\fR, which can be an integer constant or any other valid
arithmetic expression.
\fISize\fR is optional and indicates the number of bytes in the
field of interest; it can be either one, two, or four, and defaults to one;
also it must be one of these valid integer constants only and cannot be a
more complex expression.

For example, `\fBether[\fP0\fB] &\fP 1 \fB!=\fP 0' catches all multicast traffic.
The expression `\fBip[\fP0\fB] &\fP 0xf \fB!=\fP 5'
catches all IPv4 packets with options.
The expression
`\fBip[\fP6:2\fB] &\fP 0x1fff \fB=\fP 0'
catches only unfragmented IPv4 datagrams and frag zero of fragmented
IPv4 datagrams.
This check is implicitly applied to the
.BR tcp ,
.BR udp ,
.BR icmp ,
.BR sctp ,
.BR igmp ,
.BR pim ,
.BR igrp ,
.BR vrrp
and
.BR carp
index operations.
For instance, \fBtcp[\fP0\fB]\fP always means the first
byte of the TCP \fIheader\fP, and never means the first byte of an
intervening fragment.
.SH NAMED VALUES
Some offsets and field values may be expressed as names rather than
as numeric values.
The following protocol header field offsets are
available: \fBicmptype\fP (ICMP type field), \fBicmp6type\fP (ICMPv6 type field),
\fBicmpcode\fP (ICMP code field), \fBicmp6code\fP (ICMPv6 code field) and
\fBtcpflags\fP (TCP flags field).
.LP
The following ICMP type field values are available:
.BR \%icmp-echoreply ,
.BR \%icmp-unreach ,
.BR \%icmp-sourcequench ,
.BR \%icmp-redirect ,
.BR \%icmp-echo ,
.BR \%icmp-routeradvert ,
.BR \%icmp-routersolicit ,
.BR \%icmp-timxceed ,
.BR \%icmp-paramprob ,
.BR \%icmp-tstamp ,
.BR \%icmp-tstampreply ,
.BR \%icmp-ireq ,
.BR \%icmp-ireqreply ,
.BR \%icmp-maskreq ,
.BR \%icmp-maskreply .
.LP
The following ICMPv6 type field values are available:
.BR \%icmp6-destinationunreach ,
.BR \%icmp6-packettoobig ,
.BR \%icmp6-timeexceeded ,
.BR \%icmp6-parameterproblem ,
.BR \%icmp6-echo ,
.BR \%icmp6-echoreply ,
.BR \%icmp6-multicastlistenerquery ,
.BR \%icmp6-multicastlistenerreportv1 ,
.BR \%icmp6-multicastlistenerdone ,
.BR \%icmp6-routersolicit ,
.BR \%icmp6-routeradvert ,
.BR \%icmp6-neighborsolicit ,
.BR \%icmp6-neighboradvert ,
.BR \%icmp6-redirect ,
.BR \%icmp6-routerrenum ,
.BR \%icmp6-nodeinformationquery ,
.BR \%icmp6-nodeinformationresponse ,
.BR \%icmp6-ineighbordiscoverysolicit ,
.BR \%icmp6-ineighbordiscoveryadvert ,
.BR \%icmp6-multicastlistenerreportv2 ,
.BR \%icmp6-homeagentdiscoveryrequest ,
.BR \%icmp6-homeagentdiscoveryreply ,
.BR \%icmp6-mobileprefixsolicit ,
.BR \%icmp6-mobileprefixadvert ,
.BR \%icmp6-certpathsolicit ,
.BR \%icmp6-certpathadvert ,
.BR \%icmp6-multicastrouteradvert ,
.BR \%icmp6-multicastroutersolicit ,
.BR \%icmp6-multicastrouterterm .
.LP
The following TCP flags field values are available: \fBtcp-fin\fP,
\fBtcp-syn\fP, \fBtcp-rst\fP, \fBtcp-push\fP,
\fBtcp-ack\fP, \fBtcp-urg\fP, \fBtcp-ece\fP,
\fBtcp-cwr\fP.
.SH COMPOUND EXPRESSIONS
Primitives and relations may be combined using:
.LP
Parentheses.
.LP
Negation (`\fB!\fP' or `\fBnot\fP').
.LP
Concatenation (`\fB&&\fP' or `\fBand\fP').
.LP
Alternation (`\fB||\fP' or `\fBor\fP').
.LP
Negation has the highest precedence.
Alternation and concatenation have equal precedence and associate
left to right.
.LP
For primitives, if an identifier is given without a keyword, the most recent
keyword is assumed.  For example,
.in +.5i
.nf
\fBnot host\fP vs \fBand\fR ace
.fi
.in -.5i
is short for
.in +.5i
.nf
\fBnot host\fP vs \fBand host\fR ace
.fi
.in -.5i
which should not be confused with
.in +.5i
.nf
\fBnot (host \fPvs\fB and host \fPace\fB)\fR
.fi
.in -.5i
.SH PROTOCOL NAMES IN VARIOUS CONTEXTS
The table below shows which protocol names can be used in which contexts of
the currently implemented syntax.  The "name" column contains a protocol name,
which often can be used as an ID in primitives that take a protocol name
argument; in all cases except
.BR \%loopback
the protocol name is also a keyword.  If the keyword is an alias, the "see"
column refers to the main keyword.  The "abbr" column tells whether the
keyword can be used as an abbreviation (that is, if the keyword is the only
contents of a primitive, it means a more complex expression).  The "PDA"
column tells whether the keyword can be used in a packet data accessor.  The
"pqual" column tells whether the keyword can be used as a case of the
.I proto
qualifier kind.  If the name can be used as an ID for the
.B proto
case of the
.I type
qualifier kind, the "tqual ID" column shows the valid context(s).

.TS
box;
l	|l	|l	|l	|l	|l
lB	|lB	|l	|l	|l	|lB.
name	see	abbr	PDA	pqual	tqual ID
_
aarp		yes	no	no	link proto \\aarp
ah		yes	no	no	[ip|ip6] proto \\ah
arp		yes	yes	yes	link proto \\arp
atalk		yes	yes	no	link proto \\atalk
carp		yes	yes	no	ip proto \\carp
clnp		yes	no	no	iso proto \\clnp
csnp		yes	no	no
decnet		yes	yes	yes	link proto \\decnet
esis		yes	no	no	iso proto \\esis
es-is	esis
esp		yes	no	no	[ip|ip6] proto \\esp
ether	link
fddi	link
icmp		yes	yes	no	ip proto \\icmp
icmp6		yes	yes	no
igmp		yes	yes	no	ip proto \\igmp
igrp		yes	yes	no	ip proto \\igrp
iih		yes	no	no
ip		yes	yes	yes	link proto \\ip
ip6		yes	yes	yes	link proto \\ip6
ipx		yes	no	no	link proto \\ipx
isis		yes	no	yes	iso proto \\isis
is-is	isis
iso		yes	no	yes	link proto \\iso
l1		yes	no	no
l2		yes	no	no
lat		yes	yes	no	link proto \\lat
link		no	yes	yes
loopback					link proto loopback
lsp		yes	no	no
mopdl		yes	yes	no	link proto \\mopdl
moprc		yes	yes	no	link proto \\moprc
netbeui		yes	no	no	link proto \\netbeui
pim		yes	yes	no	[ip|ip6] proto \\pim
ppp	link
psnp		yes	no	no
radio		no	yes	no
rarp		yes	yes	yes	link proto \\rarp
sca		yes	yes	no	link proto \\sca
sctp		yes	yes	yes	[ip|ip6] proto \\sctp
slip	link
snp		yes	no	no
stp		yes	no	no	link proto \\stp
tcp		yes	yes	yes	[ip|ip6] proto \\tcp
tr	link
udp		yes	yes	yes	[ip|ip6] proto \\udp
vrrp		yes	yes	no	ip proto \\vrrp
wlan	link
.TE

.SH EXAMPLES
.LP
To select all packets arriving at or departing from `sundown':
.RS
.nf
\fBhost\fP sundown
.fi
.RE
.LP
To select traffic between `helios' and either `hot' or `ace':
.RS
.nf
\fBhost\fP helios \fBand (\fPhot \fBor\fP ace\fB)\fP
.fi
.RE
.LP
To select all IPv4 packets between `ace' and any host except `helios':
.RS
.nf
\fBip host\fP ace \fBand not\fP helios
.fi
.RE
.LP
To select all traffic between local hosts and hosts at Berkeley:
.RS
.nf
\fBnet\fP ucb-ether
.fi
.RE
.LP
To select all FTP traffic through Internet gateway `snup':
.RS
.nf
\fBgateway\fP snup \fBand (port\fP ftp \fBor\fP ftp-data\fB)\fP
.fi
.RE
.LP
To select IPv4 traffic neither sourced from nor destined for local hosts
(if you gateway to one other net, this stuff should never make it
onto your local net).
.RS
.nf
\fBip and not net \fPlocalnet
.fi
.RE
.LP
To select the start and end packets (the SYN and FIN packets) of each
TCP conversation that involves a non-local host.
.RS
.nf
\fBtcp[tcpflags] & (tcp-syn|tcp-fin) !=\fP 0 \fBand not src and dst net\fP localnet
.fi
.RE
.LP
To select the TCP packets with flags RST and ACK both set.
(i.e. select only the RST and ACK flags in the flags field, and if the result
is "RST and ACK both set", match)
.RS
.nf
.B
tcp[tcpflags] & (tcp-rst|tcp-ack) == (tcp-rst|tcp-ack)
.fi
.RE
.LP
To select all IPv4 HTTP packets to and from port 80, i.e. print only
packets that contain data, not, for example, SYN and FIN packets and
ACK-only packets.  (IPv6 is left as an exercise for the reader.)
.RS
.nf
\fBtcp port\fP 80 \fBand (((ip[\fP2:2\fB] - ((ip[\fP0\fB]&\fP0xf\fB)<<\fP2\fB)) - ((tcp[\fP12\fB]&\fP0xf0\fB)>>\fP2\fB)) != \fP0\fB)
.fi
.RE
.LP
To select IPv4 packets longer than 576 bytes sent through gateway `snup':
.RS
.nf
\fBgateway\fP snup \fBand ip[\fP2:2\fB] >\fP 576
.fi
.RE
.LP
To select IPv4 broadcast or multicast packets that were
.I not
sent via Ethernet broadcast or multicast:
.RS
.nf
\fBether[\fP0\fB] &\fP 1 \fB=\fP 0 \fBand ip[\fP16\fB] >=\fP 224
.fi
.RE
.LP
To select all ICMP packets that are not echo requests/replies (i.e., not
ping packets):
.RS
.nf
.B
icmp[icmptype] != icmp-echo and icmp[icmptype] != icmp-echoreply
.B
icmp6[icmp6type] != icmp6-echo and icmp6[icmp6type] != icmp6-echoreply
.fi
.RE
.SH BACKWARD COMPATIBILITY
The
.B carp
keyword became available in libpcap 1.2.1.
.PP
The
.BR hfisu ,
.BR hlssu ,
.BR hmsu ,
.BR hsio ,
.BR hopc ,
.BR hdpc
and
.B hsls
keywords became available in libpcap 1.5.3.
.PP
The modulo
.RB ( % )
and bitwise XOR
.RB ( ^ )
binary operators became available in libpcap 1.6.2.
.PP
The
.B geneve
keyword became available in libpcap 1.8.0.
.PP
The ICMPv6 type code names, as well as the
.B tcp-ece
and
.B tcp-cwr
TCP flag names became available in libpcap 1.9.0.
.PP
The
.B ifindex
keyword became available in libpcap 1.10.0.
.PP
The
.B vxlan
keyword became available in libpcap 1.11.0.
.SH SEE ALSO
.BR pcap (3PCAP)
.SH BUGS
To report a security issue please send an e-mail to \%<EMAIL>.
.LP
To report bugs and other problems, contribute patches, request a
feature, provide generic feedback etc please see the file
.I CONTRIBUTING.md
in the libpcap source tree root.
.LP
Filter expressions on fields other than those in Token Ring headers will
not correctly handle source-routed Token Ring packets.
.LP
Filter expressions on fields other than those in 802.11 headers will not
correctly handle 802.11 data packets with both To DS and From DS set.
.LP
`\fBip6 proto\fP'
should chase header chain, but at this moment it does not.
`\fBip6 protochain\fP'
is supplied for this behavior.  For example, to match IPv6 fragments:
`\fBip6 protochain\fP 44'
.LP
Arithmetic expression against transport layer headers, like \fBtcp[0]\fP,
does not work against IPv6 packets.
It only looks at IPv4 packets.
.LP
The
.B sio
and
.B hsio
keywords do not test whether the packet is an MSU packet.  The
.BR dpc ,
.BR opc ,
.BR sls ,
.BR hdpc ,
.B hopc
and
.B hsls
keywords do not test whether the packet is an MTP3 packet.
.LP
For ARP and RARP the current implementation assumes IPv4 over Ethernet and may
incorrectly match packets that have a different combination of protocol and
hardware.

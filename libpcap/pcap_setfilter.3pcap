.\" Copyright (c) 1994, 1996, 1997
.\"	The Regents of the University of California.  All rights reserved.
.\"
.\" Redistribution and use in source and binary forms, with or without
.\" modification, are permitted provided that: (1) source code distributions
.\" retain the above copyright notice and this paragraph in its entirety, (2)
.\" distributions including binary code include the above copyright notice and
.\" this paragraph in its entirety in the documentation or other materials
.\" provided with the distribution, and (3) all advertising materials mentioning
.\" features or use of this software display the following acknowledgement:
.\" ``This product includes software developed by the University of California,
.\" Lawrence Berkeley Laboratory and its contributors.'' Neither the name of
.\" the University nor the names of its contributors may be used to endorse
.\" or promote products derived from this software without specific prior
.\" written permission.
.\" THIS SOFTWARE IS PROVIDED ``AS IS'' AND WITHOUT ANY EXPRESS OR IMPLIED
.\" WARRANTIES, INCLUDING, WITHOUT LIMITATION, THE IMPLIED WARRANTIES OF
.\" MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.
.\"
.TH PCAP_SETFILTER 3PCAP "13 February 2025"
.SH NAME
pcap_setfilter \- set the filter
.SH SYNOPSIS
.nf
.ft B
#include <pcap/pcap.h>
.ft
.LP
.ft B
int pcap_setfilter(pcap_t *p, struct bpf_program *fp);
.ft
.fi
.SH DESCRIPTION
.BR pcap_setfilter ()
is used to specify a filter program.
.I fp
is a pointer to a
.I bpf_program
struct, usually the result of a call to
.BR \%pcap_compile (3PCAP).
.PP
If the calls to
.BR \%pcap_compile ()
and
.BR \%pcap_setfilter ()
use the same
.B pcap_t
handle, packet filtering should either work correctly or fail correctly
without additional efforts.  Otherwise the filter program must have been
compiled for the same link-layer header type as the one used by the
.B pcap_t
handle, also on Linux any usage of BPF extensions must match the use case.
Specifically, if the
.B pcap_t
stands for an input savefile, the filter program must not use the extensions,
but if it stands for a live packet capture, the filter program may use the
extensions if necessary.  See
.BR pcap_compile ()
for more information.
.SH RETURN VALUE
.BR pcap_setfilter ()
returns
.B 0
on success,
.B PCAP_ERROR_NOT_ACTIVATED
if called on a capture handle that has been created but not activated, or
.B PCAP_ERROR
on other errors.  If
.B PCAP_ERROR
is returned,
.BR pcap_geterr (3PCAP)
or
.BR pcap_perror (3PCAP)
may be called with
.I p
as an argument to fetch or display the error text.
.SH SEE ALSO
.BR pcap (3PCAP)

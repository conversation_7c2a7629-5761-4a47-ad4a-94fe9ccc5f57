.\" Copyright (c) 1994, 1996, 1997
.\"	The Regents of the University of California.  All rights reserved.
.\"
.\" Redistribution and use in source and binary forms, with or without
.\" modification, are permitted provided that: (1) source code distributions
.\" retain the above copyright notice and this paragraph in its entirety, (2)
.\" distributions including binary code include the above copyright notice and
.\" this paragraph in its entirety in the documentation or other materials
.\" provided with the distribution, and (3) all advertising materials mentioning
.\" features or use of this software display the following acknowledgement:
.\" ``This product includes software developed by the University of California,
.\" Lawrence Berkeley Laboratory and its contributors.'' Neither the name of
.\" the University nor the names of its contributors may be used to endorse
.\" or promote products derived from this software without specific prior
.\" written permission.
.\" THIS SOFTWARE IS PROVIDED ``AS IS'' AND WITHOUT ANY EXPRESS OR IMPLIED
.\" WARRANTIES, INCLUDING, WITHOUT LIMITATION, THE IMPLIED WARRANTIES OF
.\" MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.
.\"
.TH PCAP_CAN_SET_RFMON 3PCAP "31 July 2016"
.SH NAME
pcap_can_set_rfmon \- check whether monitor mode can be set for a
not-yet-activated capture handle
.SH SYNOPSIS
.nf
.ft B
#include <pcap/pcap.h>
.LP
.ft B
int pcap_can_set_rfmon(pcap_t *p);
.ft
.fi
.SH DESCRIPTION
.BR pcap_can_set_rfmon ()
checks whether monitor mode could be set on a capture handle when
the handle is activated.
.SH RETURN VALUE
.BR pcap_can_set_rfmon ()
returns
.B 0
if monitor mode could not be set,
.B 1
if monitor mode could be set, and a negative value on error.
A negative return value indicates what error condition occurred.
The possible error values are:
.TP
.B PCAP_ERROR_NO_SUCH_DEVICE
The capture source specified when the handle was created doesn't
exist.
.TP
.B PCAP_ERROR_PERM_DENIED
The process doesn't have permission to check whether monitor mode
could be supported.
.TP
.B PCAP_ERROR_ACTIVATED
The capture handle has already been activated.
.TP
.B PCAP_ERROR
Another error occurred.
.BR pcap_geterr (3PCAP)
or
.BR \%pcap_perror (3PCAP)
may be called with
.I p
as an argument to fetch or display a message describing the error.
.LP
Additional error codes may be added in the future; a program should
check for
.BR 0 ,
.BR 1 ,
and negative, return codes, and treat all negative
return codes as errors.
.BR pcap_statustostr (3PCAP)
can be called, with a warning or error code as an argument, to fetch a
message describing the warning or error code.
.SH SEE ALSO
.BR pcap (3PCAP),
.BR pcap_create (3PCAP),
.BR pcap_activate (3PCAP),
.BR pcap_set_rfmon (3PCAP)

.\" Copyright (c) 1994, 1996, 1997
.\"	The Regents of the University of California.  All rights reserved.
.\"
.\" Redistribution and use in source and binary forms, with or without
.\" modification, are permitted provided that: (1) source code distributions
.\" retain the above copyright notice and this paragraph in its entirety, (2)
.\" distributions including binary code include the above copyright notice and
.\" this paragraph in its entirety in the documentation or other materials
.\" provided with the distribution, and (3) all advertising materials mentioning
.\" features or use of this software display the following acknowledgement:
.\" ``This product includes software developed by the University of California,
.\" Lawrence Berkeley Laboratory and its contributors.'' Neither the name of
.\" the University nor the names of its contributors may be used to endorse
.\" or promote products derived from this software without specific prior
.\" written permission.
.\" THIS SOFTWARE IS PROVIDED ``AS IS'' AND WITHOUT ANY EXPRESS OR IMPLIED
.\" WARRANTIES, INCLUDING, WITHOUT LIMITATION, THE IMPLIED WARRANTIES OF
.\" MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.
.\"
.TH PCAP_IS_SWAPPED 3PCAP "7 April 2014"
.SH NAME
pcap_is_swapped \- find out whether a savefile has the native byte order
.SH SYNOPSIS
.nf
.ft B
#include <pcap/pcap.h>
.ft
.LP
.ft B
int pcap_is_swapped(pcap_t *p);
.ft
.fi
.SH DESCRIPTION
.BR pcap_is_swapped ()
returns true (\fB1\fP) if
.I p
refers to a ``savefile'' that uses a different byte order
than the current system.  For a live capture, it always returns false
(\fB0\fP).
.PP
It must not be called on a pcap descriptor created by
.BR \%pcap_create (3PCAP)
that has not yet been activated by
.BR \%pcap_activate (3PCAP).
.SH RETURN VALUE
.BR pcap_is_swapped ()
returns true (\fB1\fP) or false (\fB0\fP) on success and
.B PCAP_ERROR_NOT_ACTIVATED
if called on a capture handle that has been created but not activated.
.SH SEE ALSO
.BR pcap (3PCAP)

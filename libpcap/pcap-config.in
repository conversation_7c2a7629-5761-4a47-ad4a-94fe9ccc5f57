#! /bin/sh

#
# Script to give the appropriate compiler flags and linker flags
# to use when building code that uses libpcap.
#
# These variables come from the configure script, so includedir and
# libdir may be defined in terms of prefix and exec_prefix, so the
# latter must be defined as well.
#
prefix="@prefix@"
exec_prefix="@exec_prefix@"
includedir="@includedir@"
libdir="@libdir@"
LIBS="@LIBS@"
LIBS_STATIC="@LIBS_STATIC@"
VERSION="@PACKAGE_VERSION@"

usage()
{
	echo "Usage: pcap-config [ --help ] [ --version ] [ --cflags ]"
	echo "                   [ --libs | --additional-libs ]"
	echo "                   [ --static | --static-pcap-only ]"
}

static=0
static_pcap_only=0
show_cflags=0
show_libs=0
show_additional_libs=0
while [ "$#" != 0 ]
do
	case "$1" in

	--static)
		static=1
		;;

	--static-pcap-only)
		static_pcap_only=1
		;;

	--cflags)
		show_cflags=1
		;;

	--libs)
		show_libs=1
		;;

	--additional-libs)
		show_additional_libs=1
		;;

	-h|--help)
		usage
		exit 0
		;;

	--version)
		echo "$VERSION"
		exit 0
		;;

	*)
		echo "pcap-config: Invalid command-line option $1 specified" 1>&2
		usage 1>&2
		exit 1
		;;
	esac
	shift
done

#
# If we aren't installing in /usr, then provide a -L flag to let build
# processes find our library.
#
# (We must check $prefix, as $libdir isn't necessarily /usr/lib in this
# case - for example, Linux distributions for 64-bit platforms that
# also provide support for binaries for a 32-bit version of the
# platform may put the 64-bit libraries, the 32-bit libraries, or both
# in directories other than /usr/lib.)
#
if [ "$prefix" != "/usr" ]
then
	LPATH=-L$libdir
fi
if [ "$static" = 1 ]
then
	#
	# Include LIBS_STATIC so that the flags include libraries
	# containing routines that libpcap uses, and libraries
	# containing routines those libraries use, etc., so that a
	# completely statically linked program - i.e., linked only with
	# static libraries - will be linked with all necessary
	# libraries.
	#
	if [ "$show_cflags" = 1 -a "$show_libs" = 1 ]
	then
		echo "-I$includedir $LPATH -l@PACKAGE_NAME@ $LIBS_STATIC"
	elif [ "$show_cflags" = 1 -a "$show_additional_libs" = 1 ]
	then
		echo "-I$includedir $LPATH $LIBS_STATIC"
	elif [ "$show_cflags" = 1 ]
	then
		echo "-I$includedir"
	elif [ "$show_libs" = 1 ]
	then
		echo "$LPATH -l@PACKAGE_NAME@ $LIBS_STATIC"
	elif [ "$show_additional_libs" = 1 ]
	then
		echo "$LIBS_STATIC"
	fi
elif [ "$static_pcap_only" = 1 ]
then
	#
	# Include LIBS so that the flags include libraries
	# containing routines that libpcap uses, but not the libraries
	# on which libpcap depends, so that an otherwise
	# dynamically-linked program, linked statically only with
	# libpcap - i.e., linked with a static libpcap and dynamic
	# versions of other libraries - will be linked with all
	# necessary libraries.
	#
	if [ "$show_cflags" = 1 -a "$show_libs" = 1 ]
	then
		echo "-I$includedir $LPATH -l@PACKAGE_NAME@ $LIBS"
	elif [ "$show_cflags" = 1 -a "$show_additional_libs" = 1 ]
	then
		echo "-I$includedir $LPATH $LIBS"
	elif [ "$show_cflags" = 1 ]
	then
		echo "-I$includedir"
	elif [ "$show_libs" = 1 ]
	then
		echo "$LPATH -l@PACKAGE_NAME@ $LIBS"
	elif [ "$show_additional_libs" = 1 ]
	then
		echo "$LIBS"
	fi
else
	#
	# Don't included LIBS or LIBS_STATIC, for building a program
	# with a dynamic libpcap; libpcap, being a dynamic library, will
	# cause all of its dynamic-library dependencies to be pulled in
	# at run time.
	#
	# Do, however, include RPATH, to make sure that, on platforms
	# that require this, programs built with this version of
	# libpcap can find it at run time.
	#
	if [ "$show_cflags" = 1 -a "$show_libs" = 1 ]
	then
		echo "-I$includedir $LPATH @RPATH@ -l@PACKAGE_NAME@"
	elif [ "$show_cflags" = 1 -a "$show_additional_libs" = 1 ]
	then
		echo "-I$includedir"
	elif [ "$show_cflags" = 1 ]
	then
		echo "-I$includedir"
	elif [ "$show_libs" = 1 ]
	then
		echo "$LPATH @RPATH@ -l@PACKAGE_NAME@"
	fi
fi

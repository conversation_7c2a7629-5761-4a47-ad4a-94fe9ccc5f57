.\" Copyright (c) 1994, 1996, 1997
.\"	The Regents of the University of California.  All rights reserved.
.\"
.\" Redistribution and use in source and binary forms, with or without
.\" modification, are permitted provided that: (1) source code distributions
.\" retain the above copyright notice and this paragraph in its entirety, (2)
.\" distributions including binary code include the above copyright notice and
.\" this paragraph in its entirety in the documentation or other materials
.\" provided with the distribution, and (3) all advertising materials mentioning
.\" features or use of this software display the following acknowledgement:
.\" ``This product includes software developed by the University of California,
.\" Lawrence Berkeley Laboratory and its contributors.'' Neither the name of
.\" the University nor the names of its contributors may be used to endorse
.\" or promote products derived from this software without specific prior
.\" written permission.
.\" THIS SOFTWARE IS PROVIDED ``AS IS'' AND WITHOUT ANY EXPRESS OR IMPLIED
.\" WARRANTIES, INCLUDING, WITHOUT LIMITATION, THE IMPLIED WARRANTIES OF
.\" MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.
.\"
.TH PCAP_STRERROR 3PCAP "26 August 2024"
.SH NAME
pcap_strerror \- convert an errno value to a string
.SH SYNOPSIS
.nf
.ft B
#include <pcap/pcap.h>
.ft
.LP
.ft B
const char *pcap_strerror(int error);
.ft
.fi
.SH DESCRIPTION
This function returns an error message string corresponding to
.IR error .
It uses either
.BR strerror (3)
or its thread-safe variant if one is available, which currently is the case in
every supported OS.
.SH BACKWARD COMPATIBILITY
This function was not thread-safe in libpcap before 1.8.1 on Windows and
in libpcap before 1.10.5 on all other OSes.
.SH SEE ALSO
.BR pcap (3PCAP),
.BR pcap_geterr (3PCAP)

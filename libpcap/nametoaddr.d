nametoaddr.o: nametoaddr.c config.h pcap-int.h pcap/pcap.h \
 pcap/funcattrs.h pcap/compiler-tests.h pcap/pcap-inttypes.h \
 pcap/socket.h pcap/bpf.h pcap/dlt.h varattrs.h fmtutils.h \
 pcap/funcattrs.h portability.h diag-control.h pcap/compiler-tests.h \
 gencode.h pcap-types.h pcap/bpf.h pcap/namedb.h nametoaddr.h \
 thread-local.h ethertype.h llc.h
config.h:
pcap-int.h:
pcap/pcap.h:
pcap/funcattrs.h:
pcap/compiler-tests.h:
pcap/pcap-inttypes.h:
pcap/socket.h:
pcap/bpf.h:
pcap/dlt.h:
varattrs.h:
fmtutils.h:
pcap/funcattrs.h:
portability.h:
diag-control.h:
pcap/compiler-tests.h:
gencode.h:
pcap-types.h:
pcap/bpf.h:
pcap/namedb.h:
nametoaddr.h:
thread-local.h:
ethertype.h:
llc.h:

pcap-util.o: pcap-util.c config.h pcap-types.h pcap/can_socketcan.h \
 pcap/pcap-inttypes.h pcap/sll.h pcap/usb.h pcap/nflog.h pcap-int.h \
 pcap/pcap.h pcap/funcattrs.h pcap/compiler-tests.h pcap/socket.h \
 pcap/bpf.h pcap/dlt.h varattrs.h fmtutils.h pcap/funcattrs.h \
 portability.h extract.h pcap-usb-linux-common.h pcap-util.h pflog.h
config.h:
pcap-types.h:
pcap/can_socketcan.h:
pcap/pcap-inttypes.h:
pcap/sll.h:
pcap/usb.h:
pcap/nflog.h:
pcap-int.h:
pcap/pcap.h:
pcap/funcattrs.h:
pcap/compiler-tests.h:
pcap/socket.h:
pcap/bpf.h:
pcap/dlt.h:
varattrs.h:
fmtutils.h:
pcap/funcattrs.h:
portability.h:
extract.h:
pcap-usb-linux-common.h:
pcap-util.h:
pflog.h:

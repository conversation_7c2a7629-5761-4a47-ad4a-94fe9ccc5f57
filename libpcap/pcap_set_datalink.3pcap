.\" Copyright (c) 1994, 1996, 1997
.\"	The Regents of the University of California.  All rights reserved.
.\"
.\" Redistribution and use in source and binary forms, with or without
.\" modification, are permitted provided that: (1) source code distributions
.\" retain the above copyright notice and this paragraph in its entirety, (2)
.\" distributions including binary code include the above copyright notice and
.\" this paragraph in its entirety in the documentation or other materials
.\" provided with the distribution, and (3) all advertising materials mentioning
.\" features or use of this software display the following acknowledgement:
.\" ``This product includes software developed by the University of California,
.\" Lawrence Berkeley Laboratory and its contributors.'' Neither the name of
.\" the University nor the names of its contributors may be used to endorse
.\" or promote products derived from this software without specific prior
.\" written permission.
.\" THIS SOFTWARE IS PROVIDED ``AS IS'' AND WITHOUT ANY EXPRESS OR IMPLIED
.\" WARRANTIES, INCLUDING, WITHOUT LIMITATION, THE IMPLIED WARRANTIES OF
.\" MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.
.\"
.TH PCAP_SET_DATALINK 3PCAP "5 March 2022"
.SH NAME
pcap_set_datalink \- set the link-layer header type to be used by a
capture device
.SH SYNOPSIS
.nf
.ft B
#include <pcap/pcap.h>
.ft
.LP
.ft B
int pcap_set_datalink(pcap_t *p, int dlt);
.ft
.fi
.SH DESCRIPTION
.BR pcap_set_datalink ()
is used to set the current link-layer header type of the pcap descriptor
to the type specified by
.IR dlt .
.SH RETURN VALUE
.BR pcap_set_datalink ()
returns
.B 0
on success,
.B PCAP_ERROR_NOT_ACTIVATED
if called on a capture handle that has been created but not activated, or
.B PCAP_ERROR
on other errors. If
.B PCAP_ERROR
is returned,
.BR pcap_geterr (3PCAP)
or
.BR pcap_perror (3PCAP)
may be called with
.I p
as an argument to fetch or display the error text.
.SH SEE ALSO
.BR pcap (3PCAP),
.BR pcap_datalink (3PCAP),
.BR pcap_list_datalinks (3PCAP),
.BR pcap_datalink_name_to_val (3PCAP)

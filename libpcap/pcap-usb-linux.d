pcap-usb-linux.o: pcap-usb-linux.c config.h pcap/usb.h \
 pcap/pcap-inttypes.h pcap-int.h pcap/pcap.h pcap/funcattrs.h \
 pcap/compiler-tests.h pcap/socket.h pcap/bpf.h pcap/dlt.h varattrs.h \
 fmtutils.h pcap/funcattrs.h portability.h pcap-usb-linux.h \
 pcap-usb-linux-common.h extract.h diag-control.h pcap/compiler-tests.h
config.h:
pcap/usb.h:
pcap/pcap-inttypes.h:
pcap-int.h:
pcap/pcap.h:
pcap/funcattrs.h:
pcap/compiler-tests.h:
pcap/socket.h:
pcap/bpf.h:
pcap/dlt.h:
varattrs.h:
fmtutils.h:
pcap/funcattrs.h:
portability.h:
pcap-usb-linux.h:
pcap-usb-linux-common.h:
extract.h:
diag-control.h:
pcap/compiler-tests.h:

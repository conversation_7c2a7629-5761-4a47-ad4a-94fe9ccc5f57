.\" Copyright (c) 1994, 1996, 1997
.\"	The Regents of the University of California.  All rights reserved.
.\"
.\" Redistribution and use in source and binary forms, with or without
.\" modification, are permitted provided that: (1) source code distributions
.\" retain the above copyright notice and this paragraph in its entirety, (2)
.\" distributions including binary code include the above copyright notice and
.\" this paragraph in its entirety in the documentation or other materials
.\" provided with the distribution, and (3) all advertising materials mentioning
.\" features or use of this software display the following acknowledgement:
.\" ``This product includes software developed by the University of California,
.\" Lawrence Berkeley Laboratory and its contributors.'' Neither the name of
.\" the University nor the names of its contributors may be used to endorse
.\" or promote products derived from this software without specific prior
.\" written permission.
.\" THIS SOFTWARE IS PROVIDED ``AS IS'' AND WITHOUT ANY EXPRESS OR IMPLIED
.\" WARRANTIES, INCLUDING, WITHOUT LIMITATION, THE IMPLIED WARRANTIES OF
.\" MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.
.\"
.TH PCAP_SET_RFMON 3PCAP "3 January 2014"
.SH NAME
pcap_set_rfmon \- set monitor mode for a not-yet-activated capture
handle
.SH SYNOPSIS
.nf
.ft B
#include <pcap/pcap.h>
.LP
.ft B
int pcap_set_rfmon(pcap_t *p, int rfmon);
.ft
.fi
.SH DESCRIPTION
.BR pcap_set_rfmon ()
sets whether monitor mode should be set on a capture handle when
the handle is activated.
If
.I rfmon
is non-zero, monitor mode will be set, otherwise it will not be set.
.SH RETURN VALUE
.BR pcap_set_rfmon ()
returns
.B 0
on success or
.B PCAP_ERROR_ACTIVATED
if called on a capture handle that has been activated.
.SH SEE ALSO
.BR pcap (3PCAP),
.BR pcap_create (3PCAP),
.BR pcap_activate (3PCAP),
.BR pcap_can_set_rfmon (3PCAP)

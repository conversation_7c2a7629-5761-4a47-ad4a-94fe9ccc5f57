.\" Copyright (c) 1994, 1996, 1997
.\"	The Regents of the University of California.  All rights reserved.
.\"
.\" Redistribution and use in source and binary forms, with or without
.\" modification, are permitted provided that: (1) source code distributions
.\" retain the above copyright notice and this paragraph in its entirety, (2)
.\" distributions including binary code include the above copyright notice and
.\" this paragraph in its entirety in the documentation or other materials
.\" provided with the distribution, and (3) all advertising materials mentioning
.\" features or use of this software display the following acknowledgement:
.\" ``This product includes software developed by the University of California,
.\" Lawrence Berkeley Laboratory and its contributors.'' Neither the name of
.\" the University nor the names of its contributors may be used to endorse
.\" or promote products derived from this software without specific prior
.\" written permission.
.\" THIS SOFTWARE IS PROVIDED ``AS IS'' AND WITHOUT ANY EXPRESS OR IMPLIED
.\" WARRANTIES, INCLUDING, WITHOUT LIMITATION, THE IMPLIED WARRANTIES OF
.\" MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.
.\"
.TH PCAP_CLOSE 3PCAP "12 October 2024"
.SH NAME
pcap_close \- close a capture device or savefile
.SH SYNOPSIS
.nf
.ft B
#include <pcap/pcap.h>
.ft
.LP
.ft B
void pcap_close(pcap_t *p);
.ft
.fi
.SH DESCRIPTION
.BR pcap_close ()
closes the files associated with
.I p
and deallocates resources. This means that, for example, if
.I p
refers to a savefile that was opened with
.BR pcap_fopen_offline ()
or
.BR pcap_fopen_offline_with_tstamp_precision (),
the stream provided to the routine will be closed unless it is
.BR stdin .
.SH SEE ALSO
.BR pcap (3PCAP),
.BR pcap_open_offline (3PCAP)

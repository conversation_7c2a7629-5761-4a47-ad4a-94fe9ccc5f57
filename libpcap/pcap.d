pcap.o: pcap.c config.h ftmacros.h pcap-types.h diag-control.h \
 pcap/compiler-tests.h thread-local.h pcap-int.h pcap/pcap.h \
 pcap/funcattrs.h pcap/compiler-tests.h pcap/pcap-inttypes.h \
 pcap/socket.h pcap/bpf.h pcap/dlt.h varattrs.h fmtutils.h \
 pcap/funcattrs.h portability.h optimize.h ../tcpdump-5.0.0/my.h \
 pcap-usb-linux.h pcap-netfilter-linux.h
config.h:
ftmacros.h:
pcap-types.h:
diag-control.h:
pcap/compiler-tests.h:
thread-local.h:
pcap-int.h:
pcap/pcap.h:
pcap/funcattrs.h:
pcap/compiler-tests.h:
pcap/pcap-inttypes.h:
pcap/socket.h:
pcap/bpf.h:
pcap/dlt.h:
varattrs.h:
fmtutils.h:
pcap/funcattrs.h:
portability.h:
optimize.h:
../tcpdump-5.0.0/my.h:
pcap-usb-linux.h:
pcap-netfilter-linux.h:

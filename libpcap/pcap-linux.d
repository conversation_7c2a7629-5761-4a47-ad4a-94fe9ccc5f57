pcap-linux.o: pcap-linux.c config.h pcap-int.h pcap/pcap.h \
 pcap/funcattrs.h pcap/compiler-tests.h pcap/pcap-inttypes.h \
 pcap/socket.h pcap/bpf.h pcap/dlt.h varattrs.h fmtutils.h \
 pcap/funcattrs.h portability.h pcap-util.h pcap-snf.h pcap/sll.h \
 pcap/vlan.h pcap/can_socketcan.h diag-control.h pcap/compiler-tests.h
config.h:
pcap-int.h:
pcap/pcap.h:
pcap/funcattrs.h:
pcap/compiler-tests.h:
pcap/pcap-inttypes.h:
pcap/socket.h:
pcap/bpf.h:
pcap/dlt.h:
varattrs.h:
fmtutils.h:
pcap/funcattrs.h:
portability.h:
pcap-util.h:
pcap-snf.h:
pcap/sll.h:
pcap/vlan.h:
pcap/can_socketcan.h:
diag-control.h:
pcap/compiler-tests.h:

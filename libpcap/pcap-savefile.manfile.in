.\" Copyright (c) 1994, 1996, 1997
.\"	The Regents of the University of California.  All rights reserved.
.\"
.\" Redistribution and use in source and binary forms, with or without
.\" modification, are permitted provided that: (1) source code distributions
.\" retain the above copyright notice and this paragraph in its entirety, (2)
.\" distributions including binary code include the above copyright notice and
.\" this paragraph in its entirety in the documentation or other materials
.\" provided with the distribution, and (3) all advertising materials mentioning
.\" features or use of this software display the following acknowledgement:
.\" ``This product includes software developed by the University of California,
.\" Lawrence Berkeley Laboratory and its contributors.'' Neither the name of
.\" the University nor the names of its contributors may be used to endorse
.\" or promote products derived from this software without specific prior
.\" written permission.
.\" THIS SOFTWARE IS PROVIDED ``AS IS'' AND WITHOUT ANY EXPRESS OR IMPLIED
.\" WARRANTIES, INCLUDING, WITHOUT LIMITATION, THE IMPLIED WARRANTIES OF
.\" MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.
.\"
.TH PCAP-SAVEFILE @MAN_FILE_FORMATS@ "21 Jan 2025"
.SH NAME
pcap-savefile \- libpcap savefile format
.SH DESCRIPTION
NOTE: applications and libraries should, if possible, use libpcap to
read savefiles, rather than having their own code to read savefiles.
If, in the future, a new file format is supported by libpcap,
applications and libraries using libpcap to read savefiles will be able
to read the new format of savefiles, but applications and libraries
using their own code to read savefiles will have to be changed to
support the new file format.
.PP
``Savefiles'' read and written by libpcap and applications using libpcap
start with a per-file header.  The format of the per-file header is:
.RS
.TS
box;
c s
c | c
c s.
Magic number
_
Major version	Minor version
_
Reserved1
_
Reserved2
_
Snapshot length
_
Link-layer header type and additional information
.TE
.RE
.PP
The per-file header length is 24 octets.
.PP
All fields in the per-file header are in the byte order of the host
writing the file.  Normally, the first field in the per-file header is a
4-byte magic number, with the value
.BR 0xa1b2c3d4 .
The magic number, when
read by a host with the same byte order as the host that wrote the file,
will have the value
.BR 0xa1b2c3d4 ,
and, when read by a host with the
opposite byte order as the host that wrote the file, will have the value
.BR 0xd4c3b2a1 .
That allows software reading the file to determine whether
the byte order of the host that wrote the file is the same as the byte
order of the host on which the file is being read, and thus whether the
values in the per-file and per-packet headers need to be byte-swapped.
.PP
If the magic number has the value
.B 0xa1b23c4d
(with the two nibbles of
the two lower-order bytes of the magic number swapped), which would be
read as
.B 0xa1b23c4d
by a host with the same byte order as the host that
wrote the file and as
.B 0x4d3cb2a1
by a host with the opposite byte order
as the host that wrote the file, the file format is the same as for
regular files, except that the time stamps for packets are given in
seconds and nanoseconds rather than seconds and microseconds.
.PP
Following this are:
.IP
A 2-byte file format major version number; the current version number is
2 (big-endian 0x00 0x02 or little-endian 0x02 0x00).
.IP
A 2-byte file format minor version number; the current version number is
4 (big-endian 0x00 0x04 or little-endian 0x04 0x00).
.IP
A 4-byte not used - SHOULD be filled with 0 by pcap file writers, and MUST
be ignored by pcap file readers.  This value was documented by some older
implementations as "gmt to local correction" or "time zone offset".
Some older pcap file writers stored non-zero values in this field.
.IP
A 4-byte not used - SHOULD be filled with 0 by pcap file writers, and MUST
be ignored by pcap file readers.  This value was documented by some older
implementations as "accuracy of timestamps".  Some older pcap file
writers stored non-zero values in this field.
.IP
A 4-byte number giving the "snapshot length" of the capture; packets
longer than the snapshot length are truncated to the snapshot length.
Specifically, when libpcap is writing to a savefile (from a live packet
capture or otherwise), if the savefile's snapshot length is
.IR N ,
it writes to the savefile only the first
.I N
bytes of a packet longer than
.I N
bytes; when libpcap is reading from a savefile, it delivers at most
.I N
bytes for any packet in the savefile.  Other software that needs to access
savefiles directly should implement the same logic.
.IP
A 4-byte number giving the link-layer header type for packets in the
capture and optional additional information.
.IP
This format of this field is:
.PP
.nf
                     1                   2                   3
 0 1 2 3 4 5 6 7 8 9 0 1 2 3 4 5 6 7 8 9 0 1 2 3 4 5 6 7 8 9 0 1
+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
|FCS len|R|P|     Reserved3     |        Link-layer type        |
+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
.fi
.IP
The field is shown as if it were in the byte order of the host reading
or writing the file, with bit 0 being the most-significant bit of the
field and bit 31 being the least-significant bit of the field.
.IP
Link-layer type (16 bits):
A 16-bit value giving the link-layer header type for packets in the file;
see
.BR pcap-linktype (@MAN_MISC_INFO@)
for the
.B LINKTYPE_
values that can appear in this field.
.IP
Reserved3 (10 bits):
not used - MUST be set to zero by pcap writers, and MUST NOT be
interpreted by pcap readers; a reader SHOULD treat a non-zero value as
an error.
.IP
P (1 bit):
A bit that, if set, indicates that the Frame Check Sequence (FCS)
length value is present and, if not set, indicates that the FCS value is
not present.
.IP
R (1 bit):
not used - MUST be set to zero by pcap writers, and MUST NOT be
interpreted by pcap readers; a reader SHOULD treat a non-zero value as
an error.
.IP
FCS len (4 bits):
A 4-bit unsigned value giving the number of 16-bit (2-octet) words
of FCS that are appended to each packet, if the P bit is set; if the P
bit is not set, and the FCS length is not indicated by the link-layer
type value, the FCS length is unknown.  The valid values of the FCS len
field are between 0 and 15; Ethernet, for example, would have an FCS
length value of 2, corresponding to a 4-octet FCS.
.PP
Following the per-file header are zero or more packets; each packet
begins with a per-packet header, which is immediately followed by the
raw packet data.  The format of the per-packet header is:
.RS
.TS
box;
c.
Time stamp, seconds value
_
Time stamp, microseconds or nanoseconds value
_
Length of captured packet data
_
Un-truncated length of the packet data
.TE
.RE
.PP
The per-packet header length is 16 octets.
.PP
All fields in the per-packet header are in the byte order of the host
writing the file.  The per-packet header begins with a time stamp giving
the approximate time the packet was captured; the time stamp consists of
a 4-byte value, giving the time in seconds since January 1, 1970,
00:00:00 UTC, followed by a 4-byte value, giving the time in
microseconds or nanoseconds since that second, depending on the magic
number in the file header.  Following that are a 4-byte value giving the
number of bytes of captured data that follow the per-packet header and a
4-byte value giving the number of bytes that would have been present had
the packet not been truncated by the snapshot length.  The two lengths
will be equal if the number of bytes of packet data are less than or
equal to the snapshot length.
.SH SEE ALSO
.BR pcap (3PCAP)

.\" Copyright (c) 1994, 1996, 1997
.\"	The Regents of the University of California.  All rights reserved.
.\"
.\" Redistribution and use in source and binary forms, with or without
.\" modification, are permitted provided that: (1) source code distributions
.\" retain the above copyright notice and this paragraph in its entirety, (2)
.\" distributions including binary code include the above copyright notice and
.\" this paragraph in its entirety in the documentation or other materials
.\" provided with the distribution, and (3) all advertising materials mentioning
.\" features or use of this software display the following acknowledgement:
.\" ``This product includes software developed by the University of California,
.\" Lawrence Berkeley Laboratory and its contributors.'' Neither the name of
.\" the University nor the names of its contributors may be used to endorse
.\" or promote products derived from this software without specific prior
.\" written permission.
.\" THIS SOFTWARE IS PROVIDED ``AS IS'' AND WITHOUT ANY EXPRESS OR IMPLIED
.\" WARRANTIES, INCLUDING, WITHOUT LIMITATION, THE IMPLIED WARRANTIES OF
.\" MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.
.\"
.TH PCAP_CREATE 3PCAP "11 March 2025"
.SH NAME
pcap_create \- create a live capture handle
.SH SYNOPSIS
.nf
.ft B
#include <pcap/pcap.h>
.ft
.LP
.nf
.ft B
char errbuf[PCAP_ERRBUF_SIZE];
.ft
.LP
.ft B
pcap_t *pcap_create(const char *source, char *errbuf);
.ft
.fi
.SH DESCRIPTION
.BR pcap_create ()
is used to create a packet capture handle to capture packets on a device
(typically a network interface, see
.BR \%pcap_findalldevs (3PCAP)
for a more detailed explanation).
.I source
is a string that specifies the capture device to open, in this function
.B NULL
means the same as the string "any".
.I errbuf
is a buffer large enough to hold at least
.B PCAP_ERRBUF_SIZE
chars.
.PP
The returned handle must be activated with
.BR pcap_activate (3PCAP)
before packets can be captured
with it; options for the capture, such as promiscuous mode, can be set
on the handle before activating it.
.SH RETURN VALUE
.BR pcap_create ()
returns a
.B pcap_t *
on success and
.B NULL
on failure.
If
.B NULL
is returned,
.I errbuf
is filled in with an appropriate error message.
.SH SEE ALSO
.BR pcap (3PCAP)

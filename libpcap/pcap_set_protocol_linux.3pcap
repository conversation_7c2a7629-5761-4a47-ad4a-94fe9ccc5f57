.\" Copyright (c) 1994, 1996, 1997
.\"	The Regents of the University of California.  All rights reserved.
.\"
.\" Redistribution and use in source and binary forms, with or without
.\" modification, are permitted provided that: (1) source code distributions
.\" retain the above copyright notice and this paragraph in its entirety, (2)
.\" distributions including binary code include the above copyright notice and
.\" this paragraph in its entirety in the documentation or other materials
.\" provided with the distribution, and (3) all advertising materials mentioning
.\" features or use of this software display the following acknowledgement:
.\" ``This product includes software developed by the University of California,
.\" Lawrence Berkeley Laboratory and its contributors.'' Neither the name of
.\" the University nor the names of its contributors may be used to endorse
.\" or promote products derived from this software without specific prior
.\" written permission.
.\" THIS SOFTWARE IS PROVIDED ``AS IS'' AND WITHOUT ANY EXPRESS OR IMPLIED
.\" WARRANTIES, INCLUDING, WITHOUT LIMITATION, THE IMPLIED WARRANTIES OF
.\" MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.
.\"
.TH PCAP_SET_PROTOCOL_LINUX 3PCAP "22 August 2018"
.SH NAME
pcap_set_protocol_linux \- set capture protocol for a not-yet-activated
capture handle
.SH SYNOPSIS
.nf
.ft B
#include <pcap/pcap.h>
.LP
.ft B
int pcap_set_protocol_linux(pcap_t *p, int protocol);
.ft
.fi
.SH DESCRIPTION
On network interface devices on Linux,
.BR pcap_set_protocol_linux ()
sets the protocol to be used in the
.BR socket (2)
call to create a capture socket when the handle is activated.  The
argument is a link-layer protocol value, such as the values in the
.B <linux/if_ether.h>
header file, specified in host byte order.
If
.I protocol
is non-zero, packets of that protocol will be captured when the
handle is activated, otherwise, all packets will be captured.  This
function is only provided on Linux, and, if it is used on any device
other than a network interface, it will have no effect.
.LP
It should not be used in portable code; instead, a filter should be
specified with
.BR pcap_setfilter (3PCAP).
.LP
If a given network interface provides a standard link-layer header, with
a standard packet type, but provides some packet types with a different
socket-layer protocol type from the one in the link-layer header, that
packet type cannot be filtered with a filter specified with
.BR pcap_setfilter ()
but can be filtered by specifying the socket-layer protocol type using
.BR pcap_set_protocol_linux ().
.SH RETURN VALUE
.BR pcap_set_protocol_linux ()
returns
.B 0
on success or
.B PCAP_ERROR_ACTIVATED
if called on a capture handle that has been activated.
.SH BACKWARD COMPATIBILITY
This function became available in libpcap release 1.9.0.
.SH SEE ALSO
.BR pcap (3PCAP),
.BR pcap_create (3PCAP),
.BR pcap_activate (3PCAP)

.\" Copyright (c) 1994, 1996, 1997
.\"	The Regents of the University of California.  All rights reserved.
.\"
.\" Redistribution and use in source and binary forms, with or without
.\" modification, are permitted provided that: (1) source code distributions
.\" retain the above copyright notice and this paragraph in its entirety, (2)
.\" distributions including binary code include the above copyright notice and
.\" this paragraph in its entirety in the documentation or other materials
.\" provided with the distribution, and (3) all advertising materials mentioning
.\" features or use of this software display the following acknowledgement:
.\" ``This product includes software developed by the University of California,
.\" Lawrence Berkeley Laboratory and its contributors.'' Neither the name of
.\" the University nor the names of its contributors may be used to endorse
.\" or promote products derived from this software without specific prior
.\" written permission.
.\" THIS SOFTWARE IS PROVIDED ``AS IS'' AND WITHOUT ANY EXPRESS OR IMPLIED
.\" WARRANTIES, INCLUDING, WITHOUT LIMITATION, THE IMPLIED WARRANTIES OF
.\" MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.
.\"
.TH PCAP_SETNONBLOCK 3PCAP "30 November 2023"
.SH NAME
pcap_setnonblock, pcap_getnonblock \- set or get the state of
non-blocking mode on a capture device
.SH SYNOPSIS
.nf
.ft B
#include <pcap/pcap.h>
.ft
.LP
.nf
.ft B
char errbuf[PCAP_ERRBUF_SIZE];
.ft
.LP
.ft B
int pcap_setnonblock(pcap_t *p, int nonblock, char *errbuf);
int pcap_getnonblock(pcap_t *p, char *errbuf);
.ft
.fi
.SH DESCRIPTION
.BR pcap_setnonblock ()
puts a capture handle into ``non-blocking'' mode, or takes it out
of ``non-blocking'' mode, depending on whether the
.I nonblock
argument is non-zero or zero.  It has no effect on ``savefiles''.
.I errbuf
is a buffer large enough to hold at least
.B PCAP_ERRBUF_SIZE
chars.
.PP
In
``non-blocking'' mode, an attempt to read from the capture descriptor
with
.BR pcap_dispatch (3PCAP)
and
.BR pcap_next_ex (3PCAP)
will, if no packets are currently available to be read, return
.B 0
immediately rather than blocking waiting for packets to arrive.
.PP
.BR pcap_loop (3PCAP)
will loop forever, consuming CPU time when no packets are currently
available;
.BR pcap_dispatch ()
should be used instead.
.BR pcap_next (3PCAP)
will return
.B NULL
if there are no packets currently available to read;
this is indistinguishable from an error, so
.BR pcap_next_ex ()
should be used instead.
.PP
When first activated with
.BR pcap_activate (3PCAP)
or opened with
.BR pcap_open_live (3PCAP),
a capture handle is not in ``non-blocking mode''; a call to
.BR pcap_setnonblock ()
is required in order to put it into ``non-blocking'' mode.
.SH RETURN VALUE
.BR pcap_setnonblock()
return 0 on success,
.B PCAP_ERROR_NOT_ACTIVATED
if called on a capture handle that has been created but not activated,
and
.B PCAP_ERROR
for other errors.
.BR pcap_getnonblock ()
returns the current ``non-blocking'' state of the capture descriptor on
success; it always returns
.B 0
on ``savefiles''.
It returns
.B PCAP_ERROR_NOT_ACTIVATED
if called on a capture handle that has been created but not activated,
and
.B PCAP_ERROR
for other errors.
If
.B PCAP_ERROR
is returned,
.I errbuf
is filled in with an appropriate error message.
.SH SEE ALSO
.BR pcap (3PCAP),
.BR pcap_next_ex (3PCAP),
.BR pcap_geterr (3PCAP)

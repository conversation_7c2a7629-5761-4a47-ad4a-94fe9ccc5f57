.\" Copyright (c) 1994, 1996, 1997
.\"	The Regents of the University of California.  All rights reserved.
.\"
.\" Redistribution and use in source and binary forms, with or without
.\" modification, are permitted provided that: (1) source code distributions
.\" retain the above copyright notice and this paragraph in its entirety, (2)
.\" distributions including binary code include the above copyright notice and
.\" this paragraph in its entirety in the documentation or other materials
.\" provided with the distribution, and (3) all advertising materials mentioning
.\" features or use of this software display the following acknowledgement:
.\" ``This product includes software developed by the University of California,
.\" Lawrence Berkeley Laboratory and its contributors.'' Neither the name of
.\" the University nor the names of its contributors may be used to endorse
.\" or promote products derived from this software without specific prior
.\" written permission.
.\" THIS SOFTWARE IS PROVIDED ``AS IS'' AND WITHOUT ANY EXPRESS OR IMPLIED
.\" WARRANTIES, INCLUDING, WITHOUT LIMITATION, THE IMPLIED WARRANTIES OF
.\" MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.
.\"
.TH PCAP_LOOKUPDEV 3PCAP "30 November 2023"
.SH NAME
pcap_lookupdev \- find the default device on which to capture
.SH SYNOPSIS
.nf
.ft B
#include <pcap/pcap.h>
.ft
.LP
.nf
.ft B
char errbuf[PCAP_ERRBUF_SIZE];
.ft
.LP
.ft B
[DEPRECATED] char *pcap_lookupdev(char *errbuf);
.ft
.fi
.SH DESCRIPTION
.B This interface is obsoleted by
.BR pcap_findalldevs (3PCAP).
To find a default device on which to capture, call
.BR pcap_findalldevs ()
and, if the list it returns is not empty, use the first device in the
list.  (If the list is empty, there are no devices on which capture is
possible.)
.I errbuf
is a buffer large enough to hold at least
.B PCAP_ERRBUF_SIZE
chars.
.LP
.B If
.BR pcap_init (3PCAP)
.B has been called, this interface always returns
.BR NULL .
.LP
.BR pcap_lookupdev ()
returns a pointer to a string giving the name of a network device
suitable for use with
.BR pcap_create (3PCAP)
and
.BR \%pcap_activate (3PCAP),
or with
.BR pcap_open_live (3PCAP),
and with
.BR pcap_lookupnet (3PCAP).
If there is an error,
or if
.BR pcap_init (3PCAP)
has been called,
.B NULL
is returned and
.I errbuf
is filled in with an appropriate error message.
.SH SEE ALSO
.BR pcap (3PCAP)
.SH BUGS
The pointer returned by
.BR pcap_lookupdev ()
points to a static buffer; subsequent calls to
.BR pcap_lookupdev ()
in the same thread, or calls to
.BR pcap_lookupdev ()
in another thread, may overwrite that buffer.
.LP
In WinPcap and Npcap, this function may return a UTF-16 string rather
than an ASCII or UTF-8 string.

.\" Copyright (c) 1994, 1996, 1997
.\"	The Regents of the University of California.  All rights reserved.
.\"
.\" Redistribution and use in source and binary forms, with or without
.\" modification, are permitted provided that: (1) source code distributions
.\" retain the above copyright notice and this paragraph in its entirety, (2)
.\" distributions including binary code include the above copyright notice and
.\" this paragraph in its entirety in the documentation or other materials
.\" provided with the distribution, and (3) all advertising materials mentioning
.\" features or use of this software display the following acknowledgement:
.\" ``This product includes software developed by the University of California,
.\" Lawrence Berkeley Laboratory and its contributors.'' Neither the name of
.\" the University nor the names of its contributors may be used to endorse
.\" or promote products derived from this software without specific prior
.\" written permission.
.\" THIS SOFTWARE IS PROVIDED ``AS IS'' AND WITHOUT ANY EXPRESS OR IMPLIED
.\" WARRANTIES, INCLUDING, WITHOUT LIMITATION, THE IMPLIED WARRANTIES OF
.\" MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.
.\"
.TH PCAP_DUMP_OPEN 3PCAP "3 July 2020"
.SH NAME
pcap_dump_open, pcap_dump_open_append, pcap_dump_fopen \- open a file to
which to write packets
.SH SYNOPSIS
.nf
.ft B
#include <pcap/pcap.h>
.ft
.nf
.LP
.ft B
pcap_dumper_t *pcap_dump_open(pcap_t *p, const char *fname);
pcap_dumper_t *pcap_dump_open_append(pcap_t *p, const char *fname);
pcap_dumper_t *pcap_dump_fopen(pcap_t *p, FILE *fp);
.ft
.fi
.SH DESCRIPTION
.BR pcap_dump_open ()
is called to open a ``savefile'' for writing.
.I fname
specifies the name of the file to open. The file will have
the same format as those used by
.BR tcpdump (1)
and
.BR tcpslice (1).
If the file does not exist, it will be created; if the file exists, it
will be truncated and overwritten.
The name "-" is a synonym
for
.BR stdout .
.PP
.BR pcap_dump_fopen ()
is called to write data to an existing open stream
.IR fp ;
this stream will be closed by a subsequent call to
.BR pcap_dump_close (3PCAP).
The stream is assumed to be at the beginning of a file that has been
newly created or truncated, so that writes will start at the beginning
of the file.
Note that on Windows, that stream should be opened in binary mode.
.PP
.I p
is a capture or ``savefile'' handle returned by an earlier call to
.BR pcap_create (3PCAP)
and activated by an earlier call to
.BR \%pcap_activate (3PCAP),
or returned by an earlier call to
.BR \%pcap_open_offline (3PCAP),
.BR pcap_open_live (3PCAP),
or
.BR pcap_open_dead (3PCAP).
The time stamp precision, link-layer type, and snapshot length from
.I p
are used as the link-layer type and snapshot length of the output file.
.PP
.BR pcap_dump_open_append ()
is like
.BR pcap_dump_open ()
but, if the file already exists, and is a pcap file with the same byte
order as the host opening the file, and has the same time stamp
precision, link-layer header type, and snapshot length as
.IR p ,
it will write new packets at the end of the file.
.SH RETURN VALUE
A pointer to a
.B pcap_dumper_t
structure to use in subsequent
.BR pcap_dump (3PCAP)
and
.BR pcap_dump_close (3PCAP)
calls is returned on success.
.B NULL
is returned on failure.
If
.B NULL
is returned,
.BR pcap_geterr (3PCAP)
can be used to get the error text.
.SH BACKWARD COMPATIBILITY
.PP
The
.BR pcap_dump_open_append ()
function became available in libpcap release 1.7.2.  In previous
releases, there is no support for appending packets to an existing
savefile.
.SH SEE ALSO
.BR pcap (3PCAP),
.BR \%pcap-savefile (@MAN_FILE_FORMATS@)

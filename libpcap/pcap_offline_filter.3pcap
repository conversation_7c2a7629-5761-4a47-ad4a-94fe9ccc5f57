.\" Copyright (c) 1994, 1996, 1997
.\"	The Regents of the University of California.  All rights reserved.
.\"
.\" Redistribution and use in source and binary forms, with or without
.\" modification, are permitted provided that: (1) source code distributions
.\" retain the above copyright notice and this paragraph in its entirety, (2)
.\" distributions including binary code include the above copyright notice and
.\" this paragraph in its entirety in the documentation or other materials
.\" provided with the distribution, and (3) all advertising materials mentioning
.\" features or use of this software display the following acknowledgement:
.\" ``This product includes software developed by the University of California,
.\" Lawrence Berkeley Laboratory and its contributors.'' Neither the name of
.\" the University nor the names of its contributors may be used to endorse
.\" or promote products derived from this software without specific prior
.\" written permission.
.\" THIS SOFTWARE IS PROVIDED ``AS IS'' AND WITHOUT ANY EXPRESS OR IMPLIED
.\" WARRANTIES, INCLUDING, WITHOUT LIMITATION, THE IMPLIED WARRANTIES OF
.\" MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.
.\"
.TH PCAP_OFFLINE_FILTER 3PCAP "24 January 2025"
.SH NAME
pcap_offline_filter \- check whether a filter matches a packet
.SH SYNOPSIS
.nf
.ft B
#include <pcap/pcap.h>
.ft
.LP
.ft B
int pcap_offline_filter(const struct bpf_program *fp,
    const struct pcap_pkthdr *h, const u_char *pkt);
.ft
.fi
.SH DESCRIPTION
.BR pcap_offline_filter ()
checks whether a filter matches a packet.
.I fp
is a pointer to a
.I bpf_program
struct, usually the result of a call to
.BR pcap_compile (3PCAP).
.I h
points to the
.I pcap_pkthdr
structure for the packet, and
.I pkt
points to the data in the packet.
.PP
The filter program must have been compiled for a link-layer header type
that matches the packet data; also on Linux the filter must not use
BPF extensions, see
.BR \%pcap_compile ()
for more information.
.SH RETURN VALUE
.BR pcap_offline_filter ()
returns the return value of the filter program.  This will be zero if
the packet doesn't match the filter and non-zero if the packet matches
the filter.
.SH SEE ALSO
.BR pcap (3PCAP)

.\" Copyright (c) 1994, 1996, 1997
.\"	The Regents of the University of California.  All rights reserved.
.\"
.\" Redistribution and use in source and binary forms, with or without
.\" modification, are permitted provided that: (1) source code distributions
.\" retain the above copyright notice and this paragraph in its entirety, (2)
.\" distributions including binary code include the above copyright notice and
.\" this paragraph in its entirety in the documentation or other materials
.\" provided with the distribution, and (3) all advertising materials mentioning
.\" features or use of this software display the following acknowledgement:
.\" ``This product includes software developed by the University of California,
.\" Lawrence Berkeley Laboratory and its contributors.'' Neither the name of
.\" the University nor the names of its contributors may be used to endorse
.\" or promote products derived from this software without specific prior
.\" written permission.
.\" THIS SOFTWARE IS PROVIDED ``AS IS'' AND WITHOUT ANY EXPRESS OR IMPLIED
.\" WARRANTIES, INCLUDING, WITHOUT LIMITATION, THE IMPLIED WARRANTIES OF
.\" MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.
.\"
.TH PCAP_INJECT 3PCAP "23 November 2024"
.SH NAME
pcap_inject, pcap_sendpacket \- transmit a packet
.SH SYNOPSIS
.nf
.ft B
#include <pcap/pcap.h>
.ft
.LP
.ft B
int pcap_inject(pcap_t *p, const void *buf, size_t size);
int pcap_sendpacket(pcap_t *p, const u_char *buf, int size);
.ft
.fi
.SH DESCRIPTION
.BR pcap_inject ()
sends a raw packet through the network interface;
.I buf
points to the data of the packet, including the link-layer header, and
.I size
is the number of bytes in the packet.
.PP
Note that, even if you successfully open the network interface, you
might not have permission to send packets on it, or it might not support
sending packets; as
.BR pcap_open_live (3PCAP)
doesn't have a flag to indicate whether to open for capturing, sending,
or capturing and sending, you cannot request an open that supports
sending and be notified at open time whether sending will be possible.
The same limitation applies to
.BR \%pcap_create (3PCAP)
and
.BR \%pcap_activate (3PCAP).
Note also that some devices might not support sending packets.
.PP
Note that, on some platforms, the link-layer header of the packet that's
sent might not be the same as the link-layer header of the packet
supplied to
.BR pcap_inject (),
as the source link-layer address, if the header contains such an
address, might be changed to be the address assigned to the interface on
which the packet it sent, if the platform doesn't support sending
completely raw and unchanged packets.  Even worse, some drivers on some
platforms might change the link-layer type field to whatever value
libpcap used when attaching to the device, even on platforms that
.I do
nominally support sending completely raw and unchanged packets.
.PP
.BR pcap_sendpacket ()
is like
.BR pcap_inject (),
but it returns
.B 0
on success, rather than returning the number of bytes
written.
.RB ( pcap_inject ()
comes from OpenBSD;
.BR pcap_sendpacket ()
comes from WinPcap/Npcap.  Both are provided for compatibility.)
.SH RETURN VALUE
.BR pcap_inject ()
returns the number of bytes written on success,
.B PCAP_ERROR_NOT_ACTIVATED
if called on a capture handle that has been created but not activated, and
.B PCAP_ERROR
on other errors.
.PP
.BR pcap_sendpacket ()
returns
.B 0
on success,
.B PCAP_ERROR_NOT_ACTIVATED
if called on a capture handle that has been created but not activated, and
.B PCAP_ERROR
on other errors.
.PP
If
.B PCAP_ERROR
is returned,
.BR pcap_geterr (3PCAP)
or
.BR pcap_perror (3PCAP)
may be called with
.I p
as an argument to fetch or display the error text.
.SH SEE ALSO
.BR pcap (3PCAP)

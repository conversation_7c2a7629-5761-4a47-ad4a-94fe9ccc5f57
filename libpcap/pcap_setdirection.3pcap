.\" Copyright (c) 1994, 1996, 1997
.\"	The Regents of the University of California.  All rights reserved.
.\"
.\" Redistribution and use in source and binary forms, with or without
.\" modification, are permitted provided that: (1) source code distributions
.\" retain the above copyright notice and this paragraph in its entirety, (2)
.\" distributions including binary code include the above copyright notice and
.\" this paragraph in its entirety in the documentation or other materials
.\" provided with the distribution, and (3) all advertising materials mentioning
.\" features or use of this software display the following acknowledgement:
.\" ``This product includes software developed by the University of California,
.\" Lawrence Berkeley Laboratory and its contributors.'' Neither the name of
.\" the University nor the names of its contributors may be used to endorse
.\" or promote products derived from this software without specific prior
.\" written permission.
.\" THIS SOFTWARE IS PROVIDED ``AS IS'' AND WITHOUT ANY EXPRESS OR IMPLIED
.\" WARRANTIES, INCLUDING, WITHOUT LIMITATION, THE IMPLIED WARRANTIES OF
.\" MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.
.\"
.TH PCAP_SETDIRECTION 3PCAP "5 March 2022"
.SH NAME
pcap_setdirection \- set the direction for which packets will be captured
.SH SYNOPSIS
.nf
.ft B
#include <pcap/pcap.h>
.ft
.LP
.ft B
int pcap_setdirection(pcap_t *p, pcap_direction_t d);
.ft
.fi
.SH DESCRIPTION
.BR pcap_setdirection ()
is used to specify a direction that packets will be captured.
.I d
is one of the constants
.BR PCAP_D_IN ,
.B PCAP_D_OUT
or
.BR PCAP_D_INOUT .
.B PCAP_D_IN
will only capture packets received by the device,
.B PCAP_D_OUT
will only capture packets sent by the device and
.B PCAP_D_INOUT
will capture packets received by or sent by the device.
.B PCAP_D_INOUT
is the default setting if this function is not called.
.PP
.BR pcap_setdirection ()
isn't necessarily fully supported on all platforms; some platforms might
return an error for all values, and some other platforms might not
support
.BR PCAP_D_OUT .
.PP
This operation is not supported if a ``savefile'' is being read.
.SH RETURN VALUE
.BR pcap_setdirection ()
returns
.B 0
on success,
.B PCAP_ERROR_NOT_ACTIVATED
if called on a capture handle that has been created but not activated, or
.B PCAP_ERROR
on other errors. If
.B PCAP_ERROR
is returned,
.BR pcap_geterr (3PCAP)
or
.BR pcap_perror (3PCAP)
may be called with
.I p
as an argument to fetch or display the error text.
.SH SEE ALSO
.BR pcap (3PCAP)

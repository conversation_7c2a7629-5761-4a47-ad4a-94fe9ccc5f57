agent/send_data.o: agent/send_data.c agent/send.h agent/top.h \
 agent/stat.h agent/procps-private.h agent/err.h agent/readproc.h \
 agent/misc.h agent/meminfo.h agent/search.h agent/../libpcap/pcap-int.h \
 libpcap/pcap/pcap.h libpcap/pcap/funcattrs.h \
 libpcap/pcap/compiler-tests.h libpcap/pcap/pcap-inttypes.h \
 libpcap/pcap/socket.h libpcap/pcap/bpf.h libpcap/pcap/dlt.h \
 agent/../libpcap/varattrs.h agent/../libpcap/fmtutils.h \
 agent/../libpcap/pcap/funcattrs.h agent/../libpcap/portability.h \
 agent/pwcache.h agent/pids.h agent/../sar/sa1.h \
 agent/../tcpdump-5.0.0/my.h agent/thpool.h
agent/send.h:
agent/top.h:
agent/stat.h:
agent/procps-private.h:
agent/err.h:
agent/readproc.h:
agent/misc.h:
agent/meminfo.h:
agent/search.h:
agent/../libpcap/pcap-int.h:
libpcap/pcap/pcap.h:
libpcap/pcap/funcattrs.h:
libpcap/pcap/compiler-tests.h:
libpcap/pcap/pcap-inttypes.h:
libpcap/pcap/socket.h:
libpcap/pcap/bpf.h:
libpcap/pcap/dlt.h:
agent/../libpcap/varattrs.h:
agent/../libpcap/fmtutils.h:
agent/../libpcap/pcap/funcattrs.h:
agent/../libpcap/portability.h:
agent/pwcache.h:
agent/pids.h:
agent/../sar/sa1.h:
agent/../tcpdump-5.0.0/my.h:
agent/thpool.h:

#include <stdio.h>

int main() {
    char link_target1[] = "socket:[991243]";
    char link_target2[] = "/dev/pts/1";
    char link_target3[] = "pipe:[12345]";
    char link_target4[] = "socket:[999]";
    
    unsigned long inode;
    int result;
    
    printf("测试不同的符号链接内容:\n\n");
    
    // 测试1: 正确的socket格式
    result = sscanf(link_target1, "socket:[%lu]", &inode);
    printf("输入: \"%s\"\n", link_target1);
    printf("sscanf返回值: %d\n", result);
    if (result == 1) {
        printf("解析成功! inode = %lu\n", inode);
    } else {
        printf("解析失败\n");
    }
    printf("\n");
    
    // 测试2: 不是socket
    result = sscanf(link_target2, "socket:[%lu]", &inode);
    printf("输入: \"%s\"\n", link_target2);
    printf("sscanf返回值: %d\n", result);
    if (result == 1) {
        printf("解析成功! inode = %lu\n", inode);
    } else {
        printf("解析失败 (这是正确的，因为不是socket)\n");
    }
    printf("\n");
    
    // 测试3: 是pipe不是socket
    result = sscanf(link_target3, "socket:[%lu]", &inode);
    printf("输入: \"%s\"\n", link_target3);
    printf("sscanf返回值: %d\n", result);
    if (result == 1) {
        printf("解析成功! inode = %lu\n", inode);
    } else {
        printf("解析失败 (这是正确的，因为是pipe不是socket)\n");
    }
    printf("\n");
    
    // 测试4: 另一个socket
    result = sscanf(link_target4, "socket:[%lu]", &inode);
    printf("输入: \"%s\"\n", link_target4);
    printf("sscanf返回值: %d\n", result);
    if (result == 1) {
        printf("解析成功! inode = %lu\n", inode);
    } else {
        printf("解析失败\n");
    }
    
    return 0;
}

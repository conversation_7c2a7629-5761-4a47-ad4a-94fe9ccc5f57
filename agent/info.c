#include <stdio.h>
#include <stdlib.h>
#include <math.h>
#include <unistd.h>
// #include <sys/sysinfo.h>
// #include <linux/kernel.h>
// add 2024-8-9
#include "sysinfo.h"

// add 2024-8-17 for sar.c
#include "../sar/sa1.h"

#include "pwcache.h"
#include "../libpcap/pcap-int.h"
#include "../libpcap/pcap.h"
#include "../tcpdump-5.0.0/my.h"
extern struct pcap *pd;

// 高效同步机制函数声明
typedef struct {
    pthread_mutex_t mutex;
    pthread_cond_t cond;
    int count;
    int waiting;
    int generation;
} efficient_barrier_t;

extern int init_barriers();
extern int cleanup_barriers();
extern int efficient_barrier_wait(efficient_barrier_t *barrier);
extern efficient_barrier_t barrier_a_done;
extern efficient_barrier_t barrier_bc_done;

#define container_of(ptr, type, member) \
   (type *)((char *)(ptr) - (char *)&((type *)0)->member)

#include "top.h"
#include "top_nls.h"
#include <time.h>
#include <string.h>
#define FSHIFT 11                                          /* nr of bits of precision */
#define FIXED_1 (1 << FSHIFT) /* 1.0 as fixed-point */     // =2048
#define LOAD_FREQ (5 * HZ + 1)                             /* 5 sec intervals */
#define EXP_1 1884                                         /* 1/exp(5sec/1min) as fixed-point */
#define EXP_5 2014                                         /* 1/exp(5sec/5min) */
#define EXP_15 2037                                        /* 1/exp(5sec/15min) */
#define LOAD_INT(x) ((x) >> FSHIFT)                        // 计算整数
#define LOAD_FRAC(x) LOAD_INT(((x) & (FIXED_1 - 1)) * 100) // 计算小数
// cpu info
float active1 = 0;
float loads1, loads2, loads3;
float load1_old = -1;
float load5_old = -1;
float load15_old = -1;

#include <ctype.h>
//#include <curses.h>
#ifndef NUMA_DISABLE
#include <dlfcn.h>
#endif
#include <errno.h>
#include <fcntl.h>
#include <float.h>
#include <limits.h>
#include <pwd.h>
#include <signal.h>
#include <stdarg.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
//#include <term.h> // foul sob, defines all sorts of stuff...
#include <math.h>
#undef tab
#undef TTY
#include <termios.h>
#include <time.h>
#include <unistd.h>
#include <search.h>

#include <sys/ioctl.h>
#include <sys/resource.h>
#include <sys/select.h> // also available via <sys/types.h>
#include <sys/time.h>
#include <sys/types.h> // also available via <stdlib.h>

// add for perf
#include <pthread.h>

int o2;
int ths = 0;
extern os_data *os;
extern FILE *g_logfile;
extern void write_log(const char *format, ...);
/*
#define HASHSIZE  64
#define HASH(x)   ((x) & (HASHSIZE - 1))
#define P_G_SZ 33

static __thread struct pwbuf {
    struct pwbuf *next;
    uid_t uid;
    char name[P_G_SZ];
} *pw2hash[HASHSIZE];

*/

// v4.0.4

// only use once when start up
int tip = 0;
int tip1 = 0;
/*
static float        Frame_etscale;

static char UTF8_tab[] = {
   1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, // 0x00 - 0x0F
   1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, // 0x10 - 0x1F
   1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, // 0x20 - 0x2F
   1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, // 0x30 - 0x3F
   1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, // 0x40 - 0x4F
   1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, // 0x50 - 0x5F
   1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, // 0x60 - 0x6F
   1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, // 0x70 - 0x7F
  -1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1, // 0x80 - 0x8F
  -1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1, // 0x90 - 0x9F
  -1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1, // 0xA0 - 0xAF
  -1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1, // 0xB0 - 0xBF
  -1,-1, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, // 0xC0 - 0xCF, 0xC2 = begins 2
   2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, // 0xD0 - 0xDF
   3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, // 0xE0 - 0xEF, 0xE0 = begins 3
   4, 4, 4, 4, 4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1, // 0xF0 - 0xFF, 0xF0 = begins 4
};                                                 //            ( 0xF5 & beyond invalid )

static inline int utf8_cols (const unsigned char *p, int n) {
#ifndef OFF_XTRAWIDE
   wchar_t wc;

   if (n > 1) {
      (void)mbtowc(&wc, (const char *)p, n);
      // allow a zero as valid, as with a 'combining acute accent'
      if ((n = wcwidth(wc)) < 0) n = 1;
   }
   return n;
#else
   (void)p; (void)n;
   return 1;
#endif
} // end: utf8_cols


#include "stat.h"
#include "procps-private.h"
static enum stat_item Stat_items[]= {
   STAT_TIC_ID,             STAT_TIC_NUMA_NODE,
   STAT_TIC_DELTA_USER,     STAT_TIC_DELTA_SYSTEM,
   STAT_TIC_DELTA_NICE,     STAT_TIC_DELTA_IDLE,
   STAT_TIC_DELTA_IOWAIT,   STAT_TIC_DELTA_IRQ,
   STAT_TIC_DELTA_SOFTIRQ,  STAT_TIC_DELTA_STOLEN,
   STAT_TIC_DELTA_GUEST,    STAT_TIC_DELTA_GUEST_NICE,
   STAT_TIC_SUM_DELTA_USER, STAT_TIC_SUM_DELTA_SYSTEM,
#ifdef CORE_TYPE_NO
   STAT_TIC_SUM_DELTA_TOTAL };
#else
   STAT_TIC_SUM_DELTA_TOTAL, STAT_TIC_TYPE_CORE };
#endif


enum Rel_statitems {
   stat_ID, stat_NU,
   stat_US, stat_SY,
   stat_NI, stat_IL,
   stat_IO, stat_IR,
   stat_SI, stat_ST,
   stat_GU, stat_GN,
   stat_SUM_USR, stat_SUM_SYS,
#ifdef CORE_TYPE_NO
   stat_SUM_TOT };
#else
   stat_SUM_TOT, stat_COR_TYP };
#endif
struct stat_jifs {
    unsigned long long user, nice, system, idle, iowait, irq, sirq, stolen, guest, gnice;
    unsigned long long xusr, xsys, xidl, xbsy, xtot;
};

        // mem stack results extractor macro, where e=rel enum
#define MEM_VAL(e) MEMINFO_VAL(e, ul_int, Mem_stack, Mem_ctx)
struct stat_core {
    int id;
    int type;                          // 2 = p-core, 1 = e-core, 0 = unsure
    int thread_1;
    int thread_2;
    struct stat_core *next;
};

struct stat_data {
    unsigned long intr;
    unsigned long ctxt;
    unsigned long btime;
    unsigned long procs_created;
    unsigned long procs_blocked;
    unsigned long procs_running;
};

struct hist_sys {
    struct stat_data new;
    struct stat_data old;
};

struct hist_tic {
    int id;
    int numa_node;
    int count;
    struct stat_jifs new;
    struct stat_jifs old;
#ifdef CPU_IDLE_FORCED
    unsigned long edge;                // only valued/valid with cpu summary
#endif
    struct stat_core *core;
    int saved_id;
};

struct stacks_extent {
    int ext_numstacks;
    struct stacks_extent *next;
    struct stat_stack **stacks;
};

struct item_support {
    int num;                           // includes 'logical_end' delimiter
    enum stat_item *enums;             // includes 'logical_end' delimiter
};

struct ext_support {
    struct item_support *items;        // how these stacks are configured
    struct stacks_extent *extents;     // anchor for these extents
};

struct tic_support {
    int n_alloc;                       // number of below structs allocated
    int n_inuse;                       // number of below structs occupied
    struct hist_tic *tics;             // actual new/old jiffies
};


struct reap_support {
    int total;                         // independently obtained # of cpus/nodes
    struct ext_support fetch;          // extents plus items details
    struct tic_support hist;           // cpu and node jiffies management
    int n_alloc;                       // last known anchor pointers allocation
    struct stat_stack **anchor;        // reapable stacks (consolidated extents)
    int n_alloc_save;                  // last known results.stacks allocation
    struct stat_reap result;           // summary + stacks returned to caller
};

struct stat_info {
    int refcount;
    FILE *stat_fp;
    char *stat_buf;                    // grows to accommodate all /proc/stat
    int stat_buf_size;                 // current size for the above stat_buf
    int cpu_count_hwm;                 // if changed, triggers new cores scan
    struct hist_sys sys_hist;          // SYS type management
    struct hist_tic cpu_hist;          // TIC type management for cpu summary
    struct reap_support cpus;          // TIC type management for real cpus
    struct reap_support nodes;         // TIC type management for numa nodes
    struct ext_support cpu_summary;    // supports /proc/stat line #1 results
    struct ext_support select;         // support for 'procps_stat_select()'
    struct stat_reaped results;        // for return to caller after a reap
    struct stat_result get_this;       // for return to caller after a get
    struct item_support reap_items;    // items used for reap (shared among 3)
    struct item_support select_items;  // items unique to select
    time_t sav_secs;                   // used by procps_stat_get to limit i/o
    struct stat_core *cores;           // linked list, also linked from hist_tic
};
*/

#define MAXTBL(t) (int)(sizeof(t) / sizeof(t[0]))
static struct stat_reaped *Stat_reap;
static struct stat_info *Stat_ctx;
static int Cpu_cnt;
static struct stat_reaped *Stat_reap;
static int Numa_node_tot;
static int Numa_node_sel = -1;
int in_cpu = 0;
#define GRAPH_prefix_abv 12 // '.......:100['
                            /*
                             * Determine difference between total bytes versus printable
                             * characters in that passed, potentially multi-byte, string */
static int utf8_delta(const char *str)
{
   const unsigned char *p = (const unsigned char *)str;
   int clen, cnum = 0;

   while (*p)
   {
      // -1 represents a decoding error, pretend it's untranslated ...
      if (0 > (clen = UTF8_tab[*p]))
         return 0;
      cnum += utf8_cols(p, clen);
      p += clen;
   }
   return (int)((const char *)p - str) - cnum;
} // end: utf8_delta

#define MALLOC __attribute__((__malloc__))

static void *alloc_c(size_t num) MALLOC;
static void *alloc_c(size_t num)
{
   void *pv;

   if (!num)
      ++num;
   if (!(pv = calloc(1, num)))
      perror("alloc_c failed");
   return pv;
} // end: alloc_c

static void *alloc_r(void *ptr, size_t num) MALLOC;
static void *alloc_r(void *ptr, size_t num)
{
   void *pv;

   if (!num)
      ++num;
   if (!(pv = realloc(ptr, num)))
      perror("alloc_r failed");
   ;
   return pv;
} // end: alloc_r

/*
 * Determine a physical end within a potential multi-byte string
 * where maximum printable chars could be accommodated in width */
static int utf8_embody(const char *str, int width)
{
   const unsigned char *p = (const unsigned char *)str;
   int clen, cnum = 0;

   if (width > 0)
   {
      while (*p)
      {
         // -1 represents a decoding error, pretend it's untranslated ...
         if (0 > (clen = UTF8_tab[*p]))
            return width;
         if (width < (cnum += utf8_cols(p, clen)))
            break;
         p += clen;
      }
   }
   return (int)((const char *)p - str);
} // end: utf8_embody

/*
 * Like the regular justify_pad routine but this guy
 * can accommodate the multi-byte translated strings */
static const char *utf8_justify(const char *str, int width, int justr)
{
   static char l_fmt[] = "%-*.*s%s", r_fmt[] = "%*.*s%s";
   static char buf[SCREENMAX];
   char tmp[SCREENMAX];

   snprintf(tmp, sizeof(tmp), "%.*s", utf8_embody(str, width), str);
   width += utf8_delta(tmp);
   snprintf(buf, sizeof(buf), justr ? r_fmt : l_fmt, width, width, tmp, COLPADSTR);
   return buf;
} // end: utf8_justify

/*
 * This guy's responsible for interfacing with the library <stat> API
 * and reaping all cpu or numa node tics.
 * ( his task is now embarassingly small under the new api ) */
static void *cpus_refresh(void *unused)
{
   enum stat_reap_type which;

   do
   {
#ifdef THREADED_CPU
      sem_wait(&Semaphore_cpus_beg);
#endif
      which = STAT_REAP_CPUS_ONLY;
      // if (CHKw(Curwin, View_CPUNOD))
      //   which = STAT_REAP_NUMA_NODES_TOO;

      Stat_reap = procps_stat_reap(Stat_ctx, which, Stat_items, MAXTBL(Stat_items));
      if (!Stat_reap)
         // error_exit(fmtmk(N_fmt(LIB_errorcpu_fmt), __LINE__, strerror(errno)));
         perror("cpus_refresh error");
#ifndef PRETEND0NUMA
      // adapt to changes in total numa nodes (assuming it's even possible)
      if (Stat_reap->numa->total && Stat_reap->numa->total != Numa_node_tot)
      {
         Numa_node_tot = Stat_reap->numa->total;
         Numa_node_sel = -1;
      }
#endif
      if (Stat_reap->cpus->total && Stat_reap->cpus->total != Cpu_cnt)
      {
         Cpu_cnt = Stat_reap->cpus->total;
#ifdef PRETEND48CPU
         Cpu_cnt = 48;
#endif
      }
#ifdef PRETENDECORE
      {
         int i, x;
         x = Cpu_cnt - (Cpu_cnt / 4);
         for (i = 0; i < Cpu_cnt; i++)
            Stat_reap->cpus->stacks[i]->head[stat_COR_TYP].result.s_int = (i < x) ? P_CORE : E_CORE;
      }
#endif
#ifdef THREADED_CPU
      sem_post(&Semaphore_cpus_end);
   } while (1);
#else
   } while (0);
#endif
   return NULL;
   (void)unused;
} // end: cpus_refresh

// mem
#include "meminfo.h"
static struct meminfo_stack *Mem_stack;
static enum meminfo_item Mem_items[] = {
    MEMINFO_MEM_FREE, MEMINFO_MEM_USED, MEMINFO_MEM_TOTAL,
    MEMINFO_MEM_CACHED_ALL, MEMINFO_MEM_BUFFERS, MEMINFO_MEM_AVAILABLE,
    MEMINFO_SWAP_TOTAL, MEMINFO_SWAP_FREE, MEMINFO_SWAP_USED};
enum Rel_memitems
{
   mem_FRE,
   mem_USE,
   mem_TOT,
   mem_QUE,
   mem_BUF,
   mem_AVL,
   swp_TOT,
   swp_FRE,
   swp_USE
};

// update 2024-8-7
/*
struct hsearch_data
  {
    struct _ENTRY *table;
    unsigned int size;
    unsigned int filled;
  };
int hsearch_r (ENTRY __item, ACTION __action, ENTRY **__retval,
                      struct hsearch_data *__htab) __THROW;
int hcreate_r (size_t __nel, struct hsearch_data *__htab) __THROW;
void hdestroy_r (struct hsearch_data *__htab) __THROW;

struct meminfo_data {
    unsigned long Active;
    unsigned long Active_anon;         // as: Active(anon):  man 5 proc: 'to be documented'
    unsigned long Active_file;         // as: Active(file):  man 5 proc: 'to be documented'
    unsigned long AnonHugePages;
    unsigned long AnonPages;
    unsigned long Bounce;
    unsigned long Buffers;
    unsigned long Cached;
    unsigned long CmaFree;
    unsigned long CmaTotal;
    unsigned long CommitLimit;
    unsigned long Committed_AS;
    unsigned long DirectMap1G;
    unsigned long DirectMap2M;
    unsigned long DirectMap4M;
    unsigned long DirectMap4k;
    unsigned long Dirty;
    unsigned long FileHugePages;
    unsigned long FilePmdMapped;
    unsigned long HardwareCorrupted;   //  man 5 proc: 'to be documented'
    unsigned long HighFree;
    unsigned long HighTotal;
    unsigned long HugePages_Free;
    unsigned long HugePages_Rsvd;
    unsigned long HugePages_Surp;
    unsigned long HugePages_Total;
    unsigned long Hugepagesize;
    unsigned long Hugetlb;
    unsigned long Inactive;
    unsigned long Inactive_anon;       // as: Inactive(anon):  man 5 proc: 'to be documented'
    unsigned long Inactive_file;       // as: Inactive(file):  man 5 proc: 'to be documented'
    unsigned long KReclaimable;
    unsigned long KernelStack;
    unsigned long LowFree;
    unsigned long LowTotal;
    unsigned long Mapped;
    unsigned long MemAvailable;
    unsigned long MemFree;
    unsigned long MemTotal;
    unsigned long Mlocked;             //  man 5 proc: 'to be documented'
    unsigned long MmapCopy;            //  man 5 proc: 'to be documented'
    unsigned long NFS_Unstable;
    unsigned long PageTables;
    unsigned long Percpu;
    unsigned long SReclaimable;
    unsigned long SUnreclaim;
    unsigned long ShadowCallStack;
    unsigned long Shmem;
    unsigned long ShmemHugePages;
    unsigned long ShmemPmdMapped;
    unsigned long Slab;
    unsigned long SwapCached;
    unsigned long SwapFree;
    unsigned long SwapTotal;
    unsigned long Unevictable;         //  man 5 proc: 'to be documented'
    unsigned long VmallocChunk;
    unsigned long VmallocTotal;
    unsigned long VmallocUsed;
    unsigned long Writeback;
    unsigned long WritebackTmp;

    unsigned long derived_mem_cached;
    unsigned long derived_mem_hi_used;
    unsigned long derived_mem_lo_used;
    unsigned long derived_mem_used;
    unsigned long derived_swap_used;
};
struct mem_hist {
    struct meminfo_data new;
    struct meminfo_data old;
};

typedef struct meminfo_info {
    int refcount;
    int meminfo_fd;
    struct mem_hist hist;
    int numitems;
    enum meminfo_item *items;
    struct hsearch_data hashtab;
    struct meminfo_result get_this;
    time_t sav_secs;
}meminfo_info;
*/
struct meminfo_info *Mem_ctx;

// process
#include "nls.h"
#include "pids.h"
#include "misc.h"
// #include "readproc.h"
static struct pids_stack **Seed_ppt; // temporary win ppt pointer |
static int Tree_idx;
static struct pids_stack **Tree_ppt;
static int *Hide_pid;
static int Hide_tot;

static char *Pseudo_screen;
/* Current terminal screen size. */
static int Screen_cols, Screen_rows, Max_lines;
static long Hertz;
static RCF_t Rc = DEF_RCFILE;
static volatile int Frames_signal; // time to rebuild all column headers
                                   /* Support for Graphing of the View_STATES ('t') and View_MEMORY ('m')
                                      commands -- which are now both 4-way toggles */
#define GRAPH_length_max 100       // the actual bars or blocks
#define GRAPH_length_min 10        // the actual bars or blocks
#define GRAPH_prefix_std 25        // '.......: 100.0/100.0 100['
#define GRAPH_prefix_abv 12        // '.......:100['
#define GRAPH_suffix 2             // '] ' (bracket + trailing space)

/* Support for adjoining display (if terminal is wide enough) */
#ifdef TOG4_SEP_OFF
static char Adjoin_sp[] = "  ";
#define ADJOIN_space (sizeof(Adjoin_sp) - 1) // 1 for null
#else
#ifdef TOG4_SEP_STD
static char Adjoin_sp[] = "~1 ~6 ";
#else
static char Adjoin_sp[] = " ~6 ~1";
#endif
#define ADJOIN_space (sizeof(Adjoin_sp) - 5) // 1 for null + 4 unprintable
#endif
#define ADJOIN_limit 8

/* Global/Non-windows mode stuff that is NOT persistent */
static int Batch = 0, // batch mode, collect no input, dumb output
    Loops = -1,       // number of iterations, -1 loops forever
    Secure_mode = 0,  // set if some functionality restricted
    Width_mode = 0,   // set w/ 'w' - potential output override
    Thread_mode = 0;  // set w/ 'H' - show threads vs. tasks

/* Specific process id monitoring support */
static int Monpids[MONPIDMAX + 1] = {0};
static int Monpidsidx = 0;
/*
 *  --- <proc/pids.h> -------------------------------------------------- */
static struct pids_info *Pids_ctx;
static struct pids_info *Pids_ctx1;
static int Pids_itms_tot; // same as MAXTBL(Fieldstab)
// static enum pids_item *Pids_itms;           // allocated as MAXTBL(Fieldstab)
// static struct pids_fetch *Pids_reap;        // for reap or select
// static struct pids_fetch *Pids_reap1;
// #define PIDSmaxt Pids_reap->counts->total   // just a little less wordy
// #define PIDSRUN Pids_reap->counts->running
// #define PIDSmaxt1 Pids_reap1->counts->total
// #define PIDSRUN1 Pids_reap1->counts->running
static sigset_t Sigwinch_set;
// pid stack results extractor macro, where e=our EU enum, t=type, s=stack
// ( we'll exploit that <proc/pids.h> provided macro as much as possible )
// ( but many functions use their own unique tailored version for access )
#define PID_VAL(e, t, s) PIDS_VAL(e, t, s, Pids_ctx)
static WIN_t Winstk[GROUPSMAX];
static WIN_t *Curwin;
#define GRAPH_prefix_std 25 // '.......: 100.0/100.0 100['
/*######  Fields Management support  #####################################*/

/* These are our gosh darn 'Fields' !
   They MUST be kept in sync with pflags !! */
static struct
{
   int width;           // field width, if applicable
   int scale;           // scaled target, if applicable
   const int align;     // the default column alignment flag
   enum pids_item item; // the new libproc item enum identifier
} Fieldstab[] = {
// these identifiers reflect the default column alignment but they really
// contain the WIN_t flag used to check/change justification at run-time!
#define A_left Show_JRSTRS  /* toggled with lower case 'j' */
#define A_right Show_JRNUMS /* toggled with upper case 'J' */

    /* .width anomalies:
            a -1 width represents variable width columns
            a  0 width represents columns set once at startup (see zap_fieldstab)

         .width  .scale  .align    .item
         ------  ------  --------  ------------------- */
    {0, -1, A_right, PIDS_ID_PID},            // s_int    EU_PID
    {0, -1, A_right, PIDS_ID_PPID},           // s_int    EU_PPD
    {5, -1, A_right, PIDS_ID_EUID},           // u_int    EU_UED
    {8, -1, A_left, PIDS_ID_EUSER},           // str      EU_UEN
    {5, -1, A_right, PIDS_ID_RUID},           // u_int    EU_URD
    {8, -1, A_left, PIDS_ID_RUSER},           // str      EU_URN
    {5, -1, A_right, PIDS_ID_SUID},           // u_int    EU_USD
    {8, -1, A_left, PIDS_ID_SUSER},           // str      EU_USN
    {5, -1, A_right, PIDS_ID_EGID},           // u_int    EU_GID
    {8, -1, A_left, PIDS_ID_EGROUP},          // str      EU_GRP
    {0, -1, A_right, PIDS_ID_PGRP},           // s_int    EU_PGD
    {8, -1, A_left, PIDS_TTY_NAME},           // str      EU_TTY
    {0, -1, A_right, PIDS_ID_TPGID},          // s_int    EU_TPG
    {0, -1, A_right, PIDS_ID_SESSION},        // s_int    EU_SID
    {3, -1, A_right, PIDS_PRIORITY},          // s_int    EU_PRI
    {3, -1, A_right, PIDS_NICE},              // s_int    EU_NCE
    {3, -1, A_right, PIDS_NLWP},              // s_int    EU_THD
    {0, -1, A_right, PIDS_PROCESSOR},         // s_int    EU_CPN
    {5, -1, A_right, PIDS_TICS_ALL_DELTA},    // u_int    EU_CPU
    {6, -1, A_right, PIDS_TICS_ALL},          // ull_int  EU_TME
    {9, -1, A_right, PIDS_TICS_ALL},          // ull_int  EU_TM2
    {5, -1, A_right, PIDS_MEM_RES},           // ul_int   EU_MEM
    {7, SK_Kb, A_right, PIDS_MEM_VIRT},       // ul_int   EU_VRT
    {6, SK_Kb, A_right, PIDS_VM_SWAP},        // ul_int   EU_SWP
    {6, SK_Kb, A_right, PIDS_MEM_RES},        // ul_int   EU_RES
    {6, SK_Kb, A_right, PIDS_MEM_CODE},       // ul_int   EU_COD
    {7, SK_Kb, A_right, PIDS_MEM_DATA},       // ul_int   EU_DAT
    {6, SK_Kb, A_right, PIDS_MEM_SHR},        // ul_int   EU_SHR
    {4, -1, A_right, PIDS_FLT_MAJ},           // ul_int   EU_FL1
    {4, -1, A_right, PIDS_FLT_MIN},           // ul_int   EU_FL2
    {4, -1, A_right, PIDS_noop},              // ul_int   EU_DRT ( always 0 w/ since 2.6 )
    {1, -1, A_right, PIDS_STATE},             // s_ch     EU_STA
    {-1, -1, A_left, PIDS_CMD},               // str      EU_CMD
    {10, -1, A_left, PIDS_WCHAN_NAME},        // str      EU_WCH
    {8, -1, A_left, PIDS_FLAGS},              // ul_int   EU_FLG
    {-1, -1, A_left, PIDS_CGROUP},            // str      EU_CGR
    {-1, -1, A_left, PIDS_SUPGIDS},           // str      EU_SGD
    {-1, -1, A_left, PIDS_SUPGROUPS},         // str      EU_SGN
    {0, -1, A_right, PIDS_ID_TGID},           // s_int    EU_TGD
    {5, -1, A_right, PIDS_OOM_ADJ},           // s_int    EU_OOA
    {4, -1, A_right, PIDS_OOM_SCORE},         // s_int    EU_OOM
    {-1, -1, A_left, PIDS_ENVIRON},           // str      EU_ENV
    {3, -1, A_right, PIDS_FLT_MAJ_DELTA},     // s_int    EU_FV1
    {3, -1, A_right, PIDS_FLT_MIN_DELTA},     // s_int    EU_FV2
    {6, SK_Kb, A_right, PIDS_VM_USED},        // ul_int   EU_USE
    {10, -1, A_right, PIDS_NS_IPC},           // ul_int   EU_NS1
    {10, -1, A_right, PIDS_NS_MNT},           // ul_int   EU_NS2
    {10, -1, A_right, PIDS_NS_NET},           // ul_int   EU_NS3
    {10, -1, A_right, PIDS_NS_PID},           // ul_int   EU_NS4
    {10, -1, A_right, PIDS_NS_USER},          // ul_int   EU_NS5
    {10, -1, A_right, PIDS_NS_UTS},           // ul_int   EU_NS6
    {8, -1, A_left, PIDS_LXCNAME},            // str      EU_LXC
    {6, SK_Kb, A_right, PIDS_VM_RSS_ANON},    // ul_int   EU_RZA
    {6, SK_Kb, A_right, PIDS_VM_RSS_FILE},    // ul_int   EU_RZF
    {6, SK_Kb, A_right, PIDS_VM_RSS_LOCKED},  // ul_int   EU_RZL
    {6, SK_Kb, A_right, PIDS_VM_RSS_SHARED},  // ul_int   EU_RZS
    {-1, -1, A_left, PIDS_CGNAME},            // str      EU_CGN
    {0, -1, A_right, PIDS_PROCESSOR_NODE},    // s_int    EU_NMA
    {5, -1, A_right, PIDS_ID_LOGIN},          // s_int    EU_LID
    {-1, -1, A_left, PIDS_EXE},               // str      EU_EXE
    {6, SK_Kb, A_right, PIDS_SMAP_RSS},       // ul_int   EU_RSS
    {6, SK_Kb, A_right, PIDS_SMAP_PSS},       // ul_int   EU_PSS
    {6, SK_Kb, A_right, PIDS_SMAP_PSS_ANON},  // ul_int   EU_PZA
    {6, SK_Kb, A_right, PIDS_SMAP_PSS_FILE},  // ul_int   EU_PZF
    {6, SK_Kb, A_right, PIDS_SMAP_PSS_SHMEM}, // ul_int   EU_PZS
    {6, SK_Kb, A_right, PIDS_SMAP_PRV_TOTAL}, // ul_int   EU_USS
    {6, -1, A_right, PIDS_IO_READ_BYTES},     // ul_int   EU_IRB
    {5, -1, A_right, PIDS_IO_READ_OPS},       // ul_int   EU_IRO
    {6, -1, A_right, PIDS_IO_WRITE_BYTES},    // ul_int   EU_IWB
    {5, -1, A_right, PIDS_IO_WRITE_OPS},      // ul_int   EU_IWO
    {5, -1, A_right, PIDS_AUTOGRP_ID},        // s_int    EU_AGI
    {4, -1, A_right, PIDS_AUTOGRP_NICE},      // s_int    EU_AGN
    {7, -1, A_right, PIDS_TICS_BEGAN},        // ull_int  EU_TM3
    {7, -1, A_right, PIDS_TIME_ELAPSED},      // real     EU_TM4
    {6, -1, A_right, PIDS_UTILIZATION},       // real     EU_CUU
    {7, -1, A_right, PIDS_UTILIZATION_C},     // real     EU_CUC
    {10, -1, A_right, PIDS_NS_CGROUP},        // ul_int   EU_NS7
    {10, -1, A_right, PIDS_NS_TIME}           // ul_int   EU_NS8
#define eu_LAST EU_NS8
// xtra Fieldstab 'pseudo pflag' entries for the newlib interface . . . . . . .
#define eu_CMDLINE eu_LAST + 1
#define eu_TICS_ALL_C eu_LAST + 2
#define eu_ID_FUID eu_LAST + 3
#define eu_CMDLINE_V eu_LAST + 4
#define eu_ENVIRON_V eu_LAST + 5
#define eu_TREE_HID eu_LAST + 6
#define eu_TREE_LVL eu_LAST + 7
#define eu_TREE_ADD eu_LAST + 8
#define eu_RESET eu_TREE_HID // demarcation for reset to zero (PIDS_extra)
    ,
    {-1, -1, -1, PIDS_CMDLINE} // str      ( if Show_CMDLIN, eu_CMDLINE    )
    ,
    {-1, -1, -1, PIDS_TICS_ALL_C} // ull_int  ( if Show_CTIMES, eu_TICS_ALL_C )
    ,
    {-1, -1, -1, PIDS_ID_FUID} // u_int    ( if a usrseltyp, eu_ID_FUID    )
    ,
    {-1, -1, -1, PIDS_CMDLINE_V} // strv     ( if Ctrlk,       eu_CMDLINE_V  )
    ,
    {-1, -1, -1, PIDS_ENVIRON_V} // strv     ( if CtrlN,       eu_ENVIRON_V  )
    ,
    {-1, -1, -1, PIDS_extra} // s_ch     ( if Show_FOREST, eu_TREE_HID   )
    ,
    {-1, -1, -1, PIDS_extra} // s_int    ( if Show_FOREST, eu_TREE_LVL   )
    ,
    {-1, -1, -1, PIDS_extra} // s_int    ( if Show_FOREST, eu_TREE_ADD   )
#undef A_left
#undef A_right
};
/*
 * This routine simply formats whatever the caller wants and
 * returns a pointer to the resulting 'const char' string... */
static const char *fmtmk(const char *fmts, ...) __attribute__((format(printf, 1, 2)));
static const char *fmtmk(const char *fmts, ...)
{
   static char buf[BIGBUFSIZ]; // with help stuff, our buffer
   va_list va;                 // requirements now exceed 1k

   va_start(va, fmts);
   vsnprintf(buf, sizeof(buf), fmts, va);
   va_end(va);
   return (const char *)buf;
} // end: fmtmk

static char *Myname;
static int Restrict_some = 0;
/*
static struct graph_parms *Graph_cpus, *Graph_mems;
struct graph_parms {
   float adjust;               // bars/blocks scaling factor
   int   length;               // scaled length (<= GRAPH_length_max)
   int   style;                // rc.graph_cpus or rc.graph_mems
   long  total, part1, part2;  // elements to be graphed
};
*/
static char Cap_clr_eol[CAPBUFSIZ] = "", // global and/or static vars
    Cap_nl_clreos[CAPBUFSIZ] = "",       // are initialized to zeros!
    Cap_clr_scr[CAPBUFSIZ] = "",         // the assignments used here
    Cap_curs_norm[CAPBUFSIZ] = "",       // cost nothing but DO serve
    Cap_curs_huge[CAPBUFSIZ] = "",       // to remind people of those
    Cap_curs_hide[CAPBUFSIZ] = "",       // batch requirements!
    Cap_clr_eos[CAPBUFSIZ] = "",
            Cap_home[CAPBUFSIZ] = "",
            Cap_norm[CAPBUFSIZ] = "",
            Cap_reverse[CAPBUFSIZ] = "",
            Caps_off[CAPBUFSIZ] = "",
            Caps_endline[SMLBUFSIZ] = "";
#ifndef RMAN_IGNORED
static char Cap_rmam[CAPBUFSIZ] = "",
            Cap_smam[CAPBUFSIZ] = "";
/* set to 1 if writing to the last column would be troublesome
   (we don't distinguish the lowermost row from the other rows) */
static int Cap_avoid_eol = 0;
#endif
static int Cap_can_goto = 0;
/* These 'SCREEN_ROWS', 'BOT_ and 'Bot_' guys are used
   in managing the special separate bottom 'window' ... */
#define SCREEN_ROWS (Screen_rows - Bot_rsvd)
#define BOT_PRESENT (Bot_what != 0)
#define BOT_ITEMMAX 10 // Bot_item array's max size
#define BOT_MSGSMAX 10 // total entries for Msg_tab
#define BOT_UNFOCUS -1 // tab focus not established
                       // negative 'item' values won't be seen by build_headers() ...
#define BOT_DELIMIT -1 // fencepost with item array
#define BOT_ITEM_NS -2 // data for namespaces req'd
#define BOT_MSG_LOG -3 // show the most recent msgs
                       // next 4 are used when toggling window contents
#define BOT_SEP_CMA ','
#define BOT_SEP_SLS '/'
#define BOT_SEP_SMI ';'
#define BOT_SEP_SPC ' '
// 1 for horizontal separator
#define BOT_RSVD 1
#define BOT_KEEP Bot_show_func = NULL
#define BOT_TOSS                          \
   do                                     \
   {                                      \
      Bot_show_func = NULL;               \
      Bot_item[0] = BOT_DELIMIT;          \
      Bot_task = Bot_rsvd = Bot_what = 0; \
      Bot_indx = BOT_UNFOCUS;             \
   } while (0)
static int Bot_task,
    Bot_what,
    Bot_rsvd,
    Bot_indx = BOT_UNFOCUS,
    Bot_item[BOT_ITEMMAX] = {BOT_DELIMIT};
static char Bot_sep,
    *Bot_head,
    Bot_buf[BOTBUFSIZ]; // the 'environ' can be huge
typedef int (*BOT_f)(const void *, const void *);
static BOT_f Bot_focus_func;
static void (*Bot_show_func)(void);
static size_t Pseudo_size;

static struct msg_node
{
   char msg[SMLBUFSIZ];
   struct msg_node *prev;
} Msg_tab[BOT_MSGSMAX];

/* The original and new terminal definitions
   (only set when not in 'Batch' mode) */
static struct termios Tty_original, // our inherited terminal definition
#ifdef TERMIOS_ONLY
    Tty_tweaked, // for interactive 'line' input
#endif
    Tty_raw; // for unsolicited input
static int Ttychanged = 0;
static char Stdout_buf[2048];
/*
static void adj_geometry (void) {
   static size_t pseudo_max = 0;
   static int w_set = 0, w_cols = 0, w_rows = 0;
   struct winsize wz;

   Screen_cols = columns;    // <term.h>
   Screen_rows = lines;      // <term.h>

   if (-1 != ioctl(STDOUT_FILENO, TIOCGWINSZ, &wz)
   && 0 < wz.ws_col && 0 < wz.ws_row) {
      Screen_cols = wz.ws_col;
      Screen_rows = wz.ws_row;
   }

#ifndef RMAN_IGNORED
   // be crudely tolerant of crude tty emulators
   if (Cap_avoid_eol) Screen_cols--;
#endif

   // we might disappoint some folks (but they'll deserve it)
   if (Screen_cols > SCREENMAX) Screen_cols = SCREENMAX;
   if (Screen_cols < W_MIN_COL) Screen_cols = W_MIN_COL;

   if (!w_set) {
      if (Width_mode > 0)              // -w with arg, we'll try to honor
         w_cols = Width_mode;
      else
      if (Width_mode < 0) {            // -w without arg, try environment
         char *env_columns = getenv("COLUMNS"),
              *env_lines = getenv("LINES"),
              *ep;
         if (env_columns && *env_columns) {
            long t, tc = 0;
            t = strtol(env_columns, &ep, 0);
            if (!*ep && (t > 0) && (t <= 0x7fffffffL)) tc = t;
            if (0 < tc) w_cols = (int)tc;
         }
         if (env_lines && *env_lines) {
            long t, tr = 0;
            t = strtol(env_lines, &ep, 0);
            if (!*ep && (t > 0) && (t <= 0x7fffffffL)) tr = t;
            if (0 < tr) w_rows = (int)tr;
         }
         if (!w_cols) w_cols = SCREENMAX;
         if (w_cols && w_cols < W_MIN_COL) w_cols = W_MIN_COL;
         if (w_rows && w_rows < W_MIN_ROW) w_rows = W_MIN_ROW;
      }
      if (w_cols > SCREENMAX) w_cols = SCREENMAX;
      w_set = 1;
   }

   if (Batch) {
      if (w_cols) Screen_cols = w_cols;
      Screen_rows = w_rows ? w_rows : INT_MAX;
      Pseudo_size = (sizeof(*Pseudo_screen) * ROWMAXSIZ);
   } else {
      const int max_rows = INT_MAX / (sizeof(*Pseudo_screen) * ROWMAXSIZ);
      if (w_cols && w_cols < Screen_cols) Screen_cols = w_cols;
      if (w_rows && w_rows < Screen_rows) Screen_rows = w_rows;
      if (Screen_rows < 0 || Screen_rows > max_rows) Screen_rows = max_rows;
      Pseudo_size = (sizeof(*Pseudo_screen) * ROWMAXSIZ) * Screen_rows;
   }
   // we'll only grow our Pseudo_screen, never shrink it
   if (pseudo_max < Pseudo_size) {
      pseudo_max = Pseudo_size;
      Pseudo_screen = alloc_r(Pseudo_screen, pseudo_max);
   }
   // ensure each row is repainted (just in case)
   PSU_CLREOS(0);

   // prepare to customize potential cpu/memory graphs
   if (Curwin->rc.double_up) {
      int num = (Curwin->rc.double_up + 1);
      int pfx = (Curwin->rc.double_up < 2) ? GRAPH_prefix_std : GRAPH_prefix_abv;

      Graph_cpus->length = (Screen_cols - (ADJOIN_space * Curwin->rc.double_up) - (num * (pfx + GRAPH_suffix))) / num;
      if (Graph_cpus->length > GRAPH_length_max) Graph_cpus->length = GRAPH_length_max;
      if (Graph_cpus->length < GRAPH_length_min) Graph_cpus->length = GRAPH_length_min;

#ifdef TOG4_MEM_1UP
      Graph_mems->length = (Screen_cols - (GRAPH_prefix_std + GRAPH_suffix));
#else
      Graph_mems->length = (Screen_cols - ADJOIN_space - (2 * (pfx + GRAPH_suffix))) / 2;
#endif
      if (Graph_mems->length > GRAPH_length_max) Graph_mems->length = GRAPH_length_max;
      if (Graph_mems->length < GRAPH_length_min) Graph_mems->length = GRAPH_length_min;

#if !defined(TOG4_MEM_FIX) && !defined(TOG4_MEM_1UP)
      if (num > 2) {
       #define cpuGRAPH  ( GRAPH_prefix_abv + Graph_cpus->length + GRAPH_suffix )
       #define nxtGRAPH  ( cpuGRAPH + ADJOIN_space )
         int len = cpuGRAPH;
         for (;;) {
            if (len + nxtGRAPH > GRAPH_length_max) break;
            len += nxtGRAPH;
         }
         len -= (GRAPH_prefix_abv + ADJOIN_space);
         Graph_mems->length = len;
       #undef cpuGRAPH
       #undef nxtGRAPH
      }
#endif
   } else {
      Graph_cpus->length = Screen_cols - (GRAPH_prefix_std + GRAPH_length_max + GRAPH_suffix);
      if (Graph_cpus->length >= 0) Graph_cpus->length = GRAPH_length_max;
      else Graph_cpus->length = Screen_cols - GRAPH_prefix_std - GRAPH_suffix;
      if (Graph_cpus->length < GRAPH_length_min) Graph_cpus->length = GRAPH_length_min;
#ifdef TOG4_MEM_1UP
      Graph_mems->length = (Screen_cols - (GRAPH_prefix_std + GRAPH_suffix));
      if (Graph_mems->length > GRAPH_length_max) Graph_mems->length = GRAPH_length_max;
      if (Graph_mems->length < GRAPH_length_min) Graph_mems->length = GRAPH_length_min;
#else
      Graph_mems->length = Graph_cpus->length;
#endif
   }
   Graph_cpus->adjust = (float)Graph_cpus->length / 100.0;
   Graph_cpus->style  = Curwin->rc.graph_cpus;

   Graph_mems->adjust = (float)Graph_mems->length / 100.0;
   Graph_mems->style  = Curwin->rc.graph_mems;

   fflush(stdout);
   Frames_signal = BREAK_off;
} // end: adj_geometry

*/

/*
 * This guy is just our way of avoiding the overhead of the standard
 * strcat function (should the caller choose to participate) */
static inline char *scat(char *dst, const char *src)
{
   while (*dst)
      dst++;
   while ((*(dst++) = *(src++)))
      ;
   return --dst;
} // end: scat

/*
 * A calibrate_fields() *Helper* function to build the actual
 * column headers & ensure necessary item enumerators support */
static void build_headers(void)
{
#define ckITEM(f)                       \
   do                                   \
   {                                    \
      Pids_itms[f] = Fieldstab[f].item; \
   } while (0)
#define ckCMDS(w)               \
   do                           \
   {                            \
      if (CHKw(w, Show_CMDLIN)) \
         ckITEM(eu_CMDLINE);    \
   } while (0)
   FLG_t f;
   char *s;
   WIN_t *w = Curwin;
#ifdef EQUCOLHDRYES
   int x, hdrmax = 0;
#endif
   int i;

   // ensure fields not visible incur no significant library costs
   for (i = 0; i < eu_RESET; i++)
      Pids_itms[i] = PIDS_noop;
   for (; i < MAXTBL(Fieldstab); i++)
      Pids_itms[i] = PIDS_extra;

   ckITEM(EU_PID); // these 2 fields may not display,
   ckITEM(EU_STA); // yet we'll always need them both
   ckITEM(EU_CMD); // this is used with 'Y' (inspect)

   do
   {
      if (VIZISw(w))
      {
         memset((s = w->columnhdr), 0, sizeof(w->columnhdr));
         if (Rc.mode_altscr)
            s = scat(s, fmtmk("%d", w->winnum));

         for (i = 0; i < w->maxpflgs; i++)
         {
            f = w->procflgs[i];
#ifdef USE_X_COLHDR
            if (CHKw(w, Show_HICOLS) && f == w->rc.sortindx)
            {
               s = scat(s, fmtmk("%s%s", Caps_off, w->capclr_msg));
               w->hdrcaplen += strlen(Caps_off) + strlen(w->capclr_msg);
            }
#else
            if (EU_MAXPFLGS <= f)
               continue;
#endif
            ckITEM(f);
            switch (f)
            {
            case EU_CMD:
               ckCMDS(w);
               break;
            case EU_CPU:
               // cpu calculations depend on number of threads
               ckITEM(EU_THD);
               break;
            case EU_TME:
            case EU_TM2:
               // for 'cumulative' times, we'll need equivalent of cutime & cstime
               if (CHKw(w, Show_CTIMES))
                  ckITEM(eu_TICS_ALL_C);
               break;
            default:
               break;
            }
            s = scat(s, utf8_justify(N_col(f), VARcol(f) ? w->varcolsz : Fieldstab[f].width, CHKw(w, Fieldstab[f].align)));
#ifdef USE_X_COLHDR
            if (CHKw(w, Show_HICOLS) && f == w->rc.sortindx)
            {
               s = scat(s, fmtmk("%s%s", Caps_off, w->capclr_hdr));
               w->hdrcaplen += strlen(Caps_off) + strlen(w->capclr_hdr);
            }
#endif
         }
#ifdef EQUCOLHDRYES
         // prepare to even out column header lengths...
         if (hdrmax + w->hdrcaplen < (x = strlen(w->columnhdr)))
            hdrmax = x - w->hdrcaplen;
#endif
         // for 'busy' only processes, we'll need elapsed tics
         if (!CHKw(w, Show_IDLEPS))
            ckITEM(EU_CPU);
         // with forest view mode, we'll need pid, tgid, ppid & start_time...
#ifndef TREE_VCPUOFF
         if (CHKw(w, Show_FOREST))
         {
            ckITEM(EU_PPD);
            ckITEM(EU_TGD);
            ckITEM(EU_TM3);
            ckITEM(eu_TREE_HID);
            ckITEM(eu_TREE_LVL);
            ckITEM(eu_TREE_ADD);
         }
#else
         if (CHKw(w, Show_FOREST))
         {
            ckITEM(EU_PPD);
            ckITEM(EU_TGD);
            ckITEM(EU_TM3);
            ckITEM(eu_TREE_HID);
            ckITEM(eu_TREE_LVL);
         }
#endif
         // for 'u/U' filtering we need these too (old top forgot that, oops)
         if (w->usrseltyp)
         {
            ckITEM(EU_UED);
            ckITEM(EU_URD);
            ckITEM(EU_USD);
            ckITEM(eu_ID_FUID);
         }

         // we must also accommodate an out of view sort field...
         f = w->rc.sortindx;
         if (EU_CMD == f)
            ckCMDS(w);
         else
            ckITEM(f);

         // lastly, accommodate any special non-display 'tagged' needs...
         i = 0;
         while (Bot_item[i] > BOT_DELIMIT)
         {
            ckITEM(Bot_item[i]);
            ++i;
         }
      } // end: VIZISw(w)

      if (Rc.mode_altscr)
         w = w->next;
   } while (w != Curwin);

#ifdef EQUCOLHDRYES
   /* now we can finally even out column header lengths
      (we're assuming entire columnhdr was memset to '\0') */
   if (Rc.mode_altscr && SCREENMAX > Screen_cols)
      for (i = 0; i < GROUPSMAX; i++)
      {
         w = &Winstk[i];
         if (CHKw(w, Show_TASKON))
            if (hdrmax + w->hdrcaplen > (x = strlen(w->columnhdr)))
               memset(&w->columnhdr[x], ' ', hdrmax + w->hdrcaplen - x);
      }
#endif

#undef ckITEM
#undef ckCMDS
} // end: build_headers

/*
 * This guy coordinates the activities surrounding the maintenance of
 * each visible window's columns headers plus item enumerators needed */
static void calibrate_fields(void)
{
   FLG_t f;
   char *s;
   const char *h;
   WIN_t *w = Curwin;
   int i, varcolcnt, len, rc;

   // adj_geometry();

   do
   {
      if (VIZISw(w))
      {
         w->hdrcaplen = 0; // really only used with USE_X_COLHDR
         // build window's pflgsall array, establish upper bounds for maxpflgs
         for (i = 0, w->totpflgs = 0; i < EU_MAXPFLGS; i++)
         {
            if (FLDviz(w, i))
            {
               f = FLDget(w, i);
#ifdef USE_X_COLHDR
               w->pflgsall[w->totpflgs++] = f;
#else
               if (CHKw(w, Show_HICOLS) && f == w->rc.sortindx)
               {
                  w->pflgsall[w->totpflgs++] = EU_XON;
                  w->pflgsall[w->totpflgs++] = f;
                  w->pflgsall[w->totpflgs++] = EU_XOF;
               }
               else
                  w->pflgsall[w->totpflgs++] = f;
#endif
            }
         }
         if (!w->totpflgs)
            w->pflgsall[w->totpflgs++] = EU_PID;

         /* build a preliminary columns header not to exceed screen width
            while accounting for a possible leading window number */
         w->varcolsz = varcolcnt = 0;
         *(s = w->columnhdr) = '\0';
         if (Rc.mode_altscr)
            s = scat(s, " ");
         for (i = 0; i + w->begpflg < w->totpflgs; i++)
         {
            f = w->pflgsall[i + w->begpflg];
            w->procflgs[i] = f;
#ifndef USE_X_COLHDR
            if (EU_MAXPFLGS <= f)
               continue;
#endif
            h = N_col(f);
            len = (VARcol(f) ? (int)strlen(h) : Fieldstab[f].width) + COLPADSIZ;
            // oops, won't fit -- we're outta here...
            if (Screen_cols < ((int)(s - w->columnhdr) + len))
               break;
            if (VARcol(f))
            {
               ++varcolcnt;
               w->varcolsz += strlen(h);
            }
            s = scat(s, fmtmk("%*.*s", len, len, h));
         }
#ifndef USE_X_COLHDR
         if (i >= 1 && EU_XON == w->procflgs[i - 1])
            --i;
#endif

         /* establish the final maxpflgs and prepare to grow the variable column
            heading(s) via varcolsz - it may be a fib if their pflags weren't
            encountered, but that's ok because they won't be displayed anyway */
         w->maxpflgs = i;
         w->varcolsz += Screen_cols - strlen(w->columnhdr);
         if (varcolcnt)
            w->varcolsz /= varcolcnt;

         /* establish the field where all remaining fields would still
            fit within screen width, including a leading window number */
         *(s = w->columnhdr) = '\0';
         if (Rc.mode_altscr)
            s = scat(s, " ");
         w->endpflg = 0;
         for (i = w->totpflgs - 1; -1 < i; i--)
         {
            f = w->pflgsall[i];
#ifndef USE_X_COLHDR
            if (EU_MAXPFLGS <= f)
            {
               w->endpflg = i;
               continue;
            }
#endif
            h = N_col(f);
            len = (VARcol(f) ? (int)strlen(h) : Fieldstab[f].width) + COLPADSIZ;
            if (Screen_cols < ((int)(s - w->columnhdr) + len))
               break;
            s = scat(s, fmtmk("%*.*s", len, len, h));
            w->endpflg = i;
         }
#ifndef USE_X_COLHDR
         if (EU_XOF == w->pflgsall[w->endpflg])
            ++w->endpflg;
#endif
      } // end: if (VIZISw(w))

      if (Rc.mode_altscr)
         w = w->next;
      if (Rc.mode_altscr)
         w = w->next;
   } while (w != Curwin);

   build_headers();

   if ((rc = procps_pids_reset(Pids_ctx, Pids_itms, Pids_itms_tot)))
      // error_exit(fmtmk(N_fmt(LIB_errorpid_fmt), __LINE__, strerror(-rc)));
      perror("procps_pids_reset error");
} // end: calibrate_fields

/*
 * Value a window's name and make the associated group name. */
static void win_names(WIN_t *q, const char *name)
{
   /* note: sprintf/snprintf results are "undefined" when src==dst,
            according to C99 & POSIX.1-2001 (thanks adc) */
   if (q->rc.winname != name)
      snprintf(q->rc.winname, sizeof(q->rc.winname), "%s", name);
   snprintf(q->grpname, sizeof(q->grpname), "%d:%s", q->winnum, name);
} // end: win_names

/*######  Misc Color/Display support  ####################################*/

/*
static void capsmk (WIN_t *q) {
 #define tPM(a,b) tparm(a, b, 0, 0, 0, 0, 0, 0, 0, 0)
   static int capsdone = 0;

   // we must NOT disturb our 'empty' terminfo strings!
   if (Batch) return;

   // these are the unchangeable puppies, so we only do 'em once
   if (!capsdone) {
      STRLCPY(Cap_clr_eol, tIF(clr_eol))
      STRLCPY(Cap_clr_eos, tIF(clr_eos))
      STRLCPY(Cap_clr_scr, tIF(clear_screen))
      // due to the leading newline, the following must be used with care
      snprintf(Cap_nl_clreos, sizeof(Cap_nl_clreos), "\n%s", tIF(clr_eos));
      STRLCPY(Cap_curs_huge, tIF(cursor_visible))
      STRLCPY(Cap_curs_norm, tIF(cursor_normal))
      STRLCPY(Cap_curs_hide, tIF(cursor_invisible))
      STRLCPY(Cap_home, tIF(cursor_home))
      STRLCPY(Cap_norm, tIF(exit_attribute_mode))
      STRLCPY(Cap_reverse, tIF(enter_reverse_mode))
#ifndef RMAN_IGNORED
      if (!eat_newline_glitch) {
         STRLCPY(Cap_rmam, tIF(exit_am_mode))
         STRLCPY(Cap_smam, tIF(enter_am_mode))
         if (!*Cap_rmam || !*Cap_smam) {
            *Cap_rmam = '\0';
            *Cap_smam = '\0';
            if (auto_right_margin)
               Cap_avoid_eol = 1;
         }
         putp(Cap_rmam);
      }
#endif
      snprintf(Caps_off, sizeof(Caps_off), "%s%s", Cap_norm, tIF(orig_pair));
      snprintf(Caps_endline, sizeof(Caps_endline), "%s%s", Caps_off, Cap_clr_eol);
      if (tgoto(cursor_address, 1, 1)) Cap_can_goto = 1;
      capsdone = 1;
   }

   STRLCPY(q->cap_bold, CHKw(q, View_NOBOLD) ? Cap_norm : tIF(enter_bold_mode))
   if (CHKw(q, Show_COLORS) && max_colors > 0) {
      STRLCPY(q->capclr_sum, tPM(set_a_foreground, q->rc.summclr))
      snprintf(q->capclr_msg, sizeof(q->capclr_msg), "%s%s"
         , tPM(set_a_foreground, q->rc.msgsclr), Cap_reverse);
      snprintf(q->capclr_pmt, sizeof(q->capclr_pmt), "%s%s"
         , tPM(set_a_foreground, q->rc.msgsclr), q->cap_bold);
      snprintf(q->capclr_hdr, sizeof(q->capclr_hdr), "%s%s"
         , tPM(set_a_foreground, q->rc.headclr), Cap_reverse);
      snprintf(q->capclr_rownorm, sizeof(q->capclr_rownorm), "%s%s"
         , Caps_off, tPM(set_a_foreground, q->rc.taskclr));
   } else {
      q->capclr_sum[0] = '\0';
#ifdef USE_X_COLHDR
      snprintf(q->capclr_msg, sizeof(q->capclr_msg), "%s%s"
         , Cap_reverse, q->cap_bold);
#else
      STRLCPY(q->capclr_msg, Cap_reverse)
#endif
      STRLCPY(q->capclr_pmt, q->cap_bold)
      STRLCPY(q->capclr_hdr, Cap_reverse)
      STRLCPY(q->capclr_rownorm, Cap_norm)
   }

   // composite(s), so we do 'em outside and after the if
   snprintf(q->capclr_rowhigh, sizeof(q->capclr_rowhigh), "%s%s"
      , q->capclr_rownorm, CHKw(q, Show_HIBOLD) ? q->cap_bold : Cap_reverse);
 #undef tIF
 #undef tPM
} // end: capsmk
*/

static const char *Cursor_state = "";
static float Cpu_pmax;
#define AUTOX_MODE (0 > Rc.fixed_widest)

/*
 * This routine exists just to consolidate most of the messin'
 * around with the Fieldstab array and some related stuff. */
static void zap_fieldstab(void)
{
#ifdef WIDEN_COLUMN
#define maX(E) ((wtab[E].wnls > wtab[E].wmin) \
                    ? wtab[E].wnls            \
                    : wtab[E].wmin)
   static struct
   {
      int wmin; // minimum field width (-1 == variable width)
      int wnls; // translated header column requirements
      int watx; // +1 == non-scalable auto sized columns
   } wtab[EU_MAXPFLGS];
#endif
   static int once;
   int i, digits;
   char buf[8];

   if (!once)
   {
      Fieldstab[EU_CPN].width = 1;
      Fieldstab[EU_NMA].width = 2;
      Fieldstab[EU_PID].width = Fieldstab[EU_PPD].width = Fieldstab[EU_PGD].width = Fieldstab[EU_SID].width = Fieldstab[EU_TGD].width = Fieldstab[EU_TPG].width = 5;
      if (5 < (digits = (int)procps_pid_length()))
      {
         if (10 < digits)
            perror("digits error");
         Fieldstab[EU_PID].width = Fieldstab[EU_PPD].width = Fieldstab[EU_PGD].width = Fieldstab[EU_SID].width = Fieldstab[EU_TGD].width = Fieldstab[EU_TPG].width = digits;
      }
#ifdef WIDEN_COLUMN
      // identify our non-scalable auto sized columns
      wtab[EU_UED].watx = wtab[EU_UEN].watx = wtab[EU_URD].watx = wtab[EU_URN].watx = wtab[EU_USD].watx = wtab[EU_USN].watx = wtab[EU_GID].watx = wtab[EU_GRP].watx = wtab[EU_TTY].watx = wtab[EU_WCH].watx = wtab[EU_NS1].watx = wtab[EU_NS2].watx = wtab[EU_NS3].watx = wtab[EU_NS4].watx = wtab[EU_NS5].watx = wtab[EU_NS6].watx = wtab[EU_NS7].watx = wtab[EU_NS8].watx = wtab[EU_LXC].watx = wtab[EU_LID].watx = +1;
      /* establish translatable header 'column' requirements
         and ensure .width reflects the widest value */
      for (i = 0; i < EU_MAXPFLGS; i++)
      {
         wtab[i].wmin = Fieldstab[i].width;
         wtab[i].wnls = (int)strlen(N_col(i)) - utf8_delta(N_col(i));
         if (wtab[i].wmin != -1)
            Fieldstab[i].width = maX(i);
      }
#endif
      once = 1;
   }

   Cpu_pmax = 99.9;
   if (Rc.mode_irixps && Cpu_cnt > 1 && !Thread_mode)
   {
      Cpu_pmax = 100.0 * Cpu_cnt;
      if (Cpu_cnt > 1000)
      {
         if (Cpu_pmax > 9999999.0)
            Cpu_pmax = 9999999.0;
      }
      else if (Cpu_cnt > 100)
      {
         if (Cpu_cnt > 999999.0)
            Cpu_pmax = 999999.0;
      }
      else if (Cpu_cnt > 10)
      {
         if (Cpu_pmax > 99999.0)
            Cpu_pmax = 99999.0;
      }
      else
      {
         if (Cpu_pmax > 999.9)
            Cpu_pmax = 999.9;
      }
   }

#ifdef WIDEN_COLUMN
   digits = snprintf(buf, sizeof(buf), "%d", Cpu_cnt);
   if (wtab[EU_CPN].wmin < digits)
   {
      if (5 < digits)
         error_exit(N_txt(FAIL_widecpu_txt));
      wtab[EU_CPN].wmin = digits;
      Fieldstab[EU_CPN].width = maX(EU_CPN);
   }
   digits = snprintf(buf, sizeof(buf), "%d", Numa_node_tot);
   if (wtab[EU_NMA].wmin < digits)
   {
      wtab[EU_NMA].wmin = digits;
      Fieldstab[EU_NMA].width = maX(EU_NMA);
   }

   // and accommodate optional wider non-scalable columns (maybe)
   if (!AUTOX_MODE)
   {
      for (i = 0; i < EU_MAXPFLGS; i++)
      {
         if (wtab[i].watx)
            Fieldstab[i].width = Rc.fixed_widest ? Rc.fixed_widest + maX(i) : maX(i);
      }
   }
#else
   digits = snprintf(buf, sizeof(buf), "%d", Cpu_cnt);
   if (1 < digits)
   {
      if (5 < digits)
         ;
      Fieldstab[EU_CPN].width = digits;
   }
   digits = snprintf(buf, sizeof(buf), "%d", Numa_node_tot);
   if (2 < digits)
      Fieldstab[EU_NMA].width = digits;

   // and accommodate optional wider non-scalable columns (maybe)
   if (!AUTOX_MODE)
   {
      Fieldstab[EU_UED].width = Fieldstab[EU_URD].width = Fieldstab[EU_USD].width = Fieldstab[EU_GID].width = Rc.fixed_widest ? 5 + Rc.fixed_widest : 5;
      Fieldstab[EU_UEN].width = Fieldstab[EU_URN].width = Fieldstab[EU_USN].width = Fieldstab[EU_GRP].width = Rc.fixed_widest ? 8 + Rc.fixed_widest : 8;
      Fieldstab[EU_TTY].width = Fieldstab[EU_LXC].width = Rc.fixed_widest ? 8 + Rc.fixed_widest : 8;
      Fieldstab[EU_WCH].width = Rc.fixed_widest ? 10 + Rc.fixed_widest : 10;
      // the initial namespace fields
      for (i = EU_NS1; i <= EU_NS6; i++)
         Fieldstab[i].width = Rc.fixed_widest ? 10 + Rc.fixed_widest : 10;
      // the later namespace additions
      for (i = EU_NS7; i <= EU_NS8; i++)
         Fieldstab[i].width = Rc.fixed_widest ? 10 + Rc.fixed_widest : 10;
   }
#endif

   /* plus user selectable scaling */
   Fieldstab[EU_VRT].scale = Fieldstab[EU_SWP].scale = Fieldstab[EU_RES].scale = Fieldstab[EU_COD].scale = Fieldstab[EU_DAT].scale = Fieldstab[EU_SHR].scale = Fieldstab[EU_USE].scale = Fieldstab[EU_RZA].scale = Fieldstab[EU_RZF].scale = Fieldstab[EU_RZL].scale = Fieldstab[EU_RZS].scale = Fieldstab[EU_RSS].scale = Fieldstab[EU_PSS].scale = Fieldstab[EU_PZA].scale = Fieldstab[EU_PZF].scale = Fieldstab[EU_PZS].scale = Fieldstab[EU_USS].scale = Rc.task_mscale;

   // lastly, ensure we've got proper column headers...
   calibrate_fields();
#undef maX
} // end: zap_fieldstab

// end v4.0.4

// end cpu info

int get_load()
{
   struct sysinfo si;

   sysinfo(&si);
   loads1 = si.loads[0];
   loads2 = si.loads[1];
   loads3 = si.loads[2];
}
int output_load()
{

   float f = 32;
   float e = 10.24;
   get_load();
   float g0 = round(loads1 / f + e);
   float g1 = round(loads2 / f + e);
   float g2 = round(loads3 / f + e);
   printf("%lu.%02lu %lu.%02lu %lu.%02lu\n",
          LOAD_INT((int)g0), LOAD_FRAC((int)g0),
          LOAD_INT((int)g1), LOAD_FRAC((int)g1),
          LOAD_INT((int)g2), LOAD_FRAC((int)g2));
   //    return 0;
}

int get_time1(os_data *os)
{

   // 获取当前时间�?
   time_t now = time(NULL);

   // 转换为本地时�?
   struct tm *local = localtime(&now);

   // 格式化时间为字符�?
   // char time1[20];
   //  char* time1 = (char*)malloc(20 * sizeof(char));
   //	char *time2=alloc_c(20);

   strftime(os->time1, 20, "%Y-%m-%d %H:%M:%S", local);
   // add 2024-8-21 update
   //	strcpy(os->time1,time2);
   //	free(time2);
}

char *time2 = NULL; 
char *get_time2()
{

   // 获取当前时间�?
   time_t now = time(NULL);

   // 转换为本地时�?
   struct tm *local = localtime(&now);

   // 格式化时间为字符�?
   // char time1[20];
   //  char* time1 = (char*)malloc(20 * sizeof(char));
   time2 = alloc_c(20);

   strftime(time1, 20, "%Y-%m-%d %H:%M:%S", local);
   return time2;
}

int avg_load(os_data *os)
{

   float f = 32;
   float e = 10.24;
   get_load();
   float g0 = round(loads1 / f + e);
   float g1 = round(loads2 / f + e);
   float g2 = round(loads3 / f + e);
   int s1 = LOAD_INT((int)g0);
   int s2 = LOAD_FRAC((int)g0);
   snprintf(os->avg_load11, 7, "%d%s%d", s1, ".", s2);
   // add 2024-8-21 update
   // strcpy(os->avg_load11,avg_load1);
   // free(avg_load1);

   // return avg_load1;

   // float *load_avg=LOAD_INT((int)g0).LOAD_FRAC((int)g0);
}
// cpu func

/*
 * This guy's modeled on libproc's 'eight_cpu_numbers' function except
 * we preserve all cpu data in our CPU_t array which is organized
 * as follows:
 *    cpus[0] thru cpus[n] == tics for each separate cpu
 *    cpus[sumSLOT]        == tics from the 1st /proc/stat line
 *  [ and beyond sumSLOT   == tics for each cpu NUMA node ] */
/*
static CPU_t *cpus_refresh (CPU_t *cpus) {
 #define sumSLOT ( smp_num_cpus )
 #define totSLOT ( 1 + smp_num_cpus + Numa_node_tot)
   static FILE *fp = NULL;
   static int siz, sav_slot = -1;
   static char *buf;
   CPU_t *sum_ptr;                               // avoid gcc subscript bloat
   int i, num, tot_read;
#ifndef NUMA_DISABLE
   int node;
#endif
   char *bp;

   if (sav_slot != sumSLOT) {
      sav_slot = sumSLOT;
      //zap_fieldstab();
      if (fp) { fclose(fp); fp = NULL; }
      if (cpus) { free(cpus); cpus = NULL; }
   }

   if (!fp) {
      if (!(fp = fopen("/proc/stat", "r")))
                 perror("fopen /proc/stat failed");
      cpus = alloc_c(totSLOT * sizeof(CPU_t));
   }
   rewind(fp);
   fflush(fp);

 #define buffGRW 1024
   tot_read = 0;
   if (buf) buf[0] = '\0';
   else buf = alloc_c((siz = buffGRW));
   while (0 < (num = fread(buf + tot_read, 1, (siz - tot_read), fp))) {
      tot_read += num;
      if (tot_read < siz) break;
      buf = alloc_r(buf, (siz += buffGRW));
   };
   buf[tot_read] = '\0';
   bp = buf;
 #undef buffGRW

   // remember from last time around
   sum_ptr = &cpus[sumSLOT];
   memcpy(&sum_ptr->sav, &sum_ptr->cur, sizeof(CT_t));
   // then value the last slot with the cpu summary line
   if (4 > sscanf(bp, "cpu %Lu %Lu %Lu %Lu %Lu %Lu %Lu %Lu"
      , &sum_ptr->cur.u, &sum_ptr->cur.n, &sum_ptr->cur.s
      , &sum_ptr->cur.i, &sum_ptr->cur.w, &sum_ptr->cur.x
      , &sum_ptr->cur.y, &sum_ptr->cur.z))
         perror("scanf failed");;
#ifndef CPU_ZEROTICS
   sum_ptr->cur.tot = sum_ptr->cur.u + sum_ptr->cur.s
      + sum_ptr->cur.n + sum_ptr->cur.i + sum_ptr->cur.w
      + sum_ptr->cur.x + sum_ptr->cur.y + sum_ptr->cur.z;
   sum_ptr->edge =
      ((sum_ptr->cur.tot - sum_ptr->sav.tot) / smp_num_cpus) / (100 / TICS_EDGE);
#endif

#ifndef NUMA_DISABLE
   // forget all of the prior node statistics (maybe)
   //if (CHKw(Curwin, View_CPUNOD))
      memset(sum_ptr + 1, 0, Numa_node_tot * sizeof(CPU_t));
#endif

   // now value each separate cpu's tics...
   for (i = 0; i < sumSLOT; i++) {
      CPU_t *cpu_ptr = &cpus[i];               // avoid gcc subscript bloat
#ifdef PRETEND8CPUS
      bp = buf;
#endif
      bp = 1 + strchr(bp, '\n');
      // remember from last time around
      memcpy(&cpu_ptr->sav, &cpu_ptr->cur, sizeof(CT_t));
      if (4 > sscanf(bp, "cpu%d %Lu %Lu %Lu %Lu %Lu %Lu %Lu %Lu", &cpu_ptr->id
         , &cpu_ptr->cur.u, &cpu_ptr->cur.n, &cpu_ptr->cur.s
         , &cpu_ptr->cur.i, &cpu_ptr->cur.w, &cpu_ptr->cur.x
         , &cpu_ptr->cur.y, &cpu_ptr->cur.z)) {
            memmove(cpu_ptr, sum_ptr, sizeof(CPU_t));
            break;        // tolerate cpus taken offline
      }

#ifndef CPU_ZEROTICS
      cpu_ptr->edge = sum_ptr->edge;
#endif
#ifdef PRETEND8CPUS
      cpu_ptr->id = i;
#endif
#ifndef NUMA_DISABLE
      if (0
      && Numa_node_tot
      && -1 < (node = Numa_node_of_cpu(cpu_ptr->id))) {
         // use our own pointer to avoid gcc subscript bloat
         CPU_t *nod_ptr = sum_ptr + 1 + node;
         nod_ptr->cur.u += cpu_ptr->cur.u; nod_ptr->sav.u += cpu_ptr->sav.u;
         nod_ptr->cur.n += cpu_ptr->cur.n; nod_ptr->sav.n += cpu_ptr->sav.n;
         nod_ptr->cur.s += cpu_ptr->cur.s; nod_ptr->sav.s += cpu_ptr->sav.s;
         nod_ptr->cur.i += cpu_ptr->cur.i; nod_ptr->sav.i += cpu_ptr->sav.i;
         nod_ptr->cur.w += cpu_ptr->cur.w; nod_ptr->sav.w += cpu_ptr->sav.w;
         nod_ptr->cur.x += cpu_ptr->cur.x; nod_ptr->sav.x += cpu_ptr->sav.x;
         nod_ptr->cur.y += cpu_ptr->cur.y; nod_ptr->sav.y += cpu_ptr->sav.y;
         nod_ptr->cur.z += cpu_ptr->cur.z; nod_ptr->sav.z += cpu_ptr->sav.z;
#ifndef CPU_ZEROTICS
         nod_ptr->edge = sum_ptr->edge;
#endif
         cpu_ptr->node = node;
#ifndef OFF_NUMASKIP
         nod_ptr->id = -1;
#endif
      }
#endif
   } // end: for each cpu

   Cpu_faux_tot = i;      // tolerate cpus taken offline

   return cpus;
 #undef sumSLOT
 #undef totSLOT
} // end: cpus_refresh



*/

/*
 * State display *Helper* function to calc and display the state
 * percentages for a single cpu.  In this way, we can support
 * the following environments without the usual code bloat.
 *    1) single cpu machines
 *    2) modest smp boxes with room for each cpu's percentages
 *    3) massive smp guys leaving little or no room for process
 *       display and thus requiring the cpu summary toggle */
/*
static void summary_hlp (CPU_t *cpu,os_data* os) {
    we'll trim to zero if we get negative time ticks,
      which has happened with some SMP kernels (pre-2.4?)
      and when cpus are dynamically added or removed

   u_frme = TRIMz(cpu->cur.u - cpu->sav.u);
   s_frme = TRIMz(cpu->cur.s - cpu->sav.s);
   n_frme = TRIMz(cpu->cur.n - cpu->sav.n);
   i_frme = TRIMz(cpu->cur.i - cpu->sav.i);
   w_frme = TRIMz(cpu->cur.w - cpu->sav.w);
   x_frme = TRIMz(cpu->cur.x - cpu->sav.x);
   y_frme = TRIMz(cpu->cur.y - cpu->sav.y);
   z_frme = TRIMz(cpu->cur.z - cpu->sav.z);
   tot_frme = u_frme + s_frme + n_frme + i_frme + w_frme + x_frme + y_frme + z_frme;
//   printf("cpu->id=%d tot_frme=%ld u_frme=%ld\n",cpu->id,tot_frme,u_frme);
#ifdef CPU_ZEROTICS
   if (1 > tot_frme) tot_frme = 1;
#else
   if (tot_frme < cpu->edge)
      tot_frme = u_frme = s_frme = n_frme = i_frme = w_frme = x_frme = y_frme = z_frme = 0;
   if (1 > tot_frme) i_frme = tot_frme = 1;
#endif
   scale = 100.0 / (float)tot_frme;

 //printf("(float)u_frme * scale=%f\n",(float)round(u_frme * scale*10)/10);
   char s1[6],s2[6],s3[6],s4[6],s5[6],s6[6],s7[6],s8[6];
   snprintf(s1,6,"%.1f", (float)round(u_frme * scale*10)/10);
   snprintf(s2,6,"%.1f", (float)round(s_frme * scale*10)/10);
   snprintf(s3,6,"%.1f", (float)round(n_frme * scale*10)/10);
   snprintf(s4,6,"%.1f", (float)round(i_frme * scale*10)/10);
   snprintf(s5,6,"%.1f", (float)round(w_frme * scale*10)/10);
   snprintf(s6,6,"%.1f", (float)round(x_frme * scale*10)/10);
   snprintf(s7,6,"%.1f", (float)round(y_frme * scale*10)/10);
   snprintf(s8,6,"%.1f", (float)round(z_frme * scale*10)/10);
  os->cpu_p.u_frme=u_frme;
  os->cpu_p.u_f=cpu->cur.u;
  strcpy(os->cpu_p.u_frme_p, s1);
  strcpy(os->cpu_p.s_frme_p, s2);
  strcpy(os->cpu_p.n_frme_p, s3);
  strcpy(os->cpu_p.i_frme_p, s4);
  strcpy(os->cpu_p.w_frme_p, s5);
  strcpy(os->cpu_p.x_frme_p, s6);
  strcpy(os->cpu_p.y_frme_p, s7);
  strcpy(os->cpu_p.z_frme_p, s8);





   if (0) {
      static struct {
         const char *user, *syst, *type;
      } gtab[] = {
         { "%-.*s~7", "%-.*s~8", Graph_bars },
         { "%-.*s~4", "%-.*s~6", Graph_blks }
      };
      char user[SMLBUFSIZ], syst[SMLBUFSIZ], dual[MEDBUFSIZ];
      int ix = Curwin->rc.graph_cpus - 1;
      float pct_user = (float)(u_frme + n_frme) * scale,
            pct_syst = (float)s_frme * scale;
#ifndef QUICK_GRAPHS
      int num_user = (int)((pct_user * Graph_adj) + .5),
          num_syst = (int)((pct_syst * Graph_adj) + .5);
      if (num_user + num_syst > Graph_len) --num_syst;
      snprintf(user, sizeof(user), gtab[ix].user, num_user, gtab[ix].type);
      snprintf(syst, sizeof(syst), gtab[ix].syst, num_syst, gtab[ix].type);
#else
      snprintf(user, sizeof(user), gtab[ix].user, (int)((pct_user * Graph_adj) + .5), gtab[ix].type);
      snprintf(syst, sizeof(syst), gtab[ix].syst, (int)((pct_syst * Graph_adj) + .4), gtab[ix].type);
#endif
      snprintf(dual, sizeof(dual), "%s%s", user, syst);

      show_special(0, fmtmk("%%%s ~3%#5.1f~2/%-#5.1f~3 %3.0f[~1%-*s]~1\n"
         , pfx, pct_user, pct_syst, pct_user + pct_syst, Graph_len +4, dual));
   } else {
      show_special(0, fmtmk(Cpu_States_fmts, pfx
         , (float)u_frme * scale, (float)s_frme * scale
         , (float)n_frme * scale, (float)i_frme * scale
         , (float)w_frme * scale, (float)x_frme * scale
         , (float)y_frme * scale, (float)z_frme * scale));
   }
 #undef TRIMz
} // end: summary_hlp

*/

// end cpu func

// mem

/* This macro opens filename only if necessary and seeks to 0 so
 * that successive calls to the functions are more efficient.
 * It also reads the current contents of the file into the global buf.
 */
#define FILE_TO_BUF(filename, fd)                            \
   do                                                        \
   {                                                         \
      static int local_n;                                    \
      if (fd == -1 && (fd = open(filename, O_RDONLY)) == -1) \
      {                                                      \
         fputs(BAD_OPEN_MESSAGE, stderr);                    \
         fflush(NULL);                                       \
         _exit(102);                                         \
      }                                                      \
      lseek(fd, 0L, SEEK_SET);                               \
      if ((local_n = read(fd, buf, sizeof buf - 1)) < 0)     \
      {                                                      \
         perror(filename);                                   \
         fflush(NULL);                                       \
         _exit(103);                                         \
      }                                                      \
      buf[local_n] = '\0';                                   \
   } while (0)
// As of 2.6.24 /proc/meminfo seems to need 888 on 64-bit,
// and would need 1258 if the obsolete fields were there.
// As of 3.13 /proc/vmstat needs 2623,
// and /proc/stat needs 3076.
static char buf[8192];
#define MEMINFO_FILE "/proc/meminfo"
static int meminfo_fd = -1;
#define BAD_OPEN_MESSAGE                                          \
   "Error: /proc must be mounted\n"                               \
   "  To mount /proc at boot you need an /etc/fstab line like:\n" \
   "      proc   /proc   proc    defaults\n"                      \
   "  In the meantime, run \"mount proc /proc -t proc\"\n"

// #define LINUX_VERSION(x,y,z)   (0x10000*(x) + 0x100*(y) + z)

int linux_version_code;
#define VM_MIN_FREE_FILE "/proc/sys/vm/min_free_kbytes"
static int vm_min_free_fd = -1;
/* return minimum of two values */
#define MIN(x, y) ((x) < (y) ? (x) : (y))

/***********************************************************************/
/*
 * Copyright 1999 by Albert Cahalan; all rights reserved.
 * This file may be used subject to the terms and conditions of the
 * GNU Library General Public License Version 2, or any later version
 * at your option, as published by the Free Software Foundation.
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
 * GNU Library General Public License for more details.
 */

typedef struct mem_table_struct
{
   const char *name;    /* memory type name */
   unsigned long *slot; /* slot in return struct */
} mem_table_struct;

static int compare_mem_table_structs(const void *a, const void *b)
{
   return strcmp(((const mem_table_struct *)a)->name, ((const mem_table_struct *)b)->name);
}

/* example data, following junk, with comments added:
 *
 * MemTotal:        61768 kB    old
 * MemFree:          1436 kB    old
 * Buffers:          1312 kB    old
 * Cached:          20932 kB    old
 * Active:          12464 kB    new
 * Inact_dirty:      7772 kB    new
 * Inact_clean:      2008 kB    new
 * Inact_target:        0 kB    new
 * Inact_laundry:       0 kB    new, and might be missing too
 * HighTotal:           0 kB
 * HighFree:            0 kB
 * LowTotal:        61768 kB
 * LowFree:          1436 kB
 * SwapTotal:      122580 kB    old
 * SwapFree:        60352 kB    old
 * Inactive:        20420 kB    2.5.41+
 * Dirty:               0 kB    2.5.41+
 * Writeback:           0 kB    2.5.41+
 * Mapped:           9792 kB    2.5.41+
 * Shmem:              28 kB    2.6.32+
 * Slab:             4564 kB    2.5.41+
 * Committed_AS:     8440 kB    2.5.41+
 * PageTables:        304 kB    2.5.41+
 * ReverseMaps:      5738       2.5.41+
 * SwapCached:          0 kB    2.5.??+
 * HugePages_Total:   220       2.5.??+
 * HugePages_Free:    138       2.5.??+
 * Hugepagesize:     4096 kB    2.5.??+
 */

/* Shmem in 2.6.32+ */
unsigned long kb_main_shared;
/* old but still kicking -- the important stuff */
static unsigned long kb_page_cache;
unsigned long kb_main_buffers;
unsigned long kb_main_free;
unsigned long kb_main_total;
unsigned long kb_swap_free;
unsigned long kb_swap_total;
/* recently introduced */
unsigned long kb_high_free;
unsigned long kb_high_total;
unsigned long kb_low_free;
unsigned long kb_low_total;
unsigned long kb_main_available;
/* 2.4.xx era */
unsigned long kb_active;
unsigned long kb_inact_laundry;
unsigned long kb_inact_dirty;
unsigned long kb_inact_clean;
unsigned long kb_inact_target;
unsigned long kb_swap_cached; /* late 2.4 and 2.6+ only */
/* derived values */
unsigned long kb_main_cached;
unsigned long kb_swap_used;
unsigned long kb_main_used;
/* 2.5.41+ */
unsigned long kb_writeback;
unsigned long kb_slab;
unsigned long nr_reversemaps;
unsigned long kb_committed_as;
unsigned long kb_dirty;
unsigned long kb_inactive;
unsigned long kb_mapped;
unsigned long kb_pagetables;
// seen on a 2.6.x kernel:
static unsigned long kb_vmalloc_chunk;
static unsigned long kb_vmalloc_total;
static unsigned long kb_vmalloc_used;
// seen on 2.6.24-rc6-git12
static unsigned long kb_anon_pages;
static unsigned long kb_bounce;
static unsigned long kb_commit_limit;
static unsigned long kb_nfs_unstable;
// seen on 2.6.18
static unsigned long kb_min_free;
// 2.6.19+
static unsigned long kb_slab_reclaimable;
static unsigned long kb_slab_unreclaimable;
// 2.6.27+
static unsigned long kb_active_file;
static unsigned long kb_inactive_file;

void meminfo(void)
{
   char namebuf[32]; /* big enough to hold any row name */
   mem_table_struct findme = {namebuf, NULL};
   mem_table_struct *found;
   char *head;
   char *tail;
   static const mem_table_struct mem_table[] = {
       {"Active", &kb_active}, // important
       {"Active(file)", &kb_active_file},
       {"AnonPages", &kb_anon_pages},
       {"Bounce", &kb_bounce},
       {"Buffers", &kb_main_buffers}, // important
       {"Cached", &kb_page_cache},    // important
       {"CommitLimit", &kb_commit_limit},
       {"Committed_AS", &kb_committed_as},
       {"Dirty", &kb_dirty}, // kB version of vmstat nr_dirty
       {"HighFree", &kb_high_free},
       {"HighTotal", &kb_high_total},
       {"Inact_clean", &kb_inact_clean},
       {"Inact_dirty", &kb_inact_dirty},
       {"Inact_laundry", &kb_inact_laundry},
       {"Inact_target", &kb_inact_target},
       {"Inactive", &kb_inactive}, // important
       {"Inactive(file)", &kb_inactive_file},
       {"LowFree", &kb_low_free},
       {"LowTotal", &kb_low_total},
       {"Mapped", &kb_mapped},               // kB version of vmstat nr_mapped
       {"MemAvailable", &kb_main_available}, // important
       {"MemFree", &kb_main_free},           // important
       {"MemTotal", &kb_main_total},         // important
       {"NFS_Unstable", &kb_nfs_unstable},
       {"PageTables", &kb_pagetables},         // kB version of vmstat nr_page_table_pages
       {"ReverseMaps", &nr_reversemaps},       // same as vmstat nr_page_table_pages
       {"SReclaimable", &kb_slab_reclaimable}, // "slab reclaimable" (dentry and inode structures)
       {"SUnreclaim", &kb_slab_unreclaimable},
       {"Shmem", &kb_main_shared}, // kernel 2.6.32 and later
       {"Slab", &kb_slab},         // kB version of vmstat nr_slab
       {"SwapCached", &kb_swap_cached},
       {"SwapFree", &kb_swap_free},   // important
       {"SwapTotal", &kb_swap_total}, // important
       {"VmallocChunk", &kb_vmalloc_chunk},
       {"VmallocTotal", &kb_vmalloc_total},
       {"VmallocUsed", &kb_vmalloc_used},
       {"Writeback", &kb_writeback}, // kB version of vmstat nr_writeback
   };
   const int mem_table_count = sizeof(mem_table) / sizeof(mem_table_struct);
   unsigned long watermark_low;
   signed long mem_available;

   FILE_TO_BUF(MEMINFO_FILE, meminfo_fd);

   kb_inactive = ~0UL;
   kb_low_total = kb_main_available = 0;

   head = buf;
   for (;;)
   {
      tail = strchr(head, ':');
      if (!tail)
         break;
      *tail = '\0';
      if (strlen(head) >= sizeof(namebuf))
      {
         head = tail + 1;
         goto nextline;
      }
      strcpy(namebuf, head);
      found = bsearch(&findme, mem_table, mem_table_count,
                      sizeof(mem_table_struct), compare_mem_table_structs);
      head = tail + 1;
      if (!found)
         goto nextline;
      *(found->slot) = (unsigned long)strtoull(head, &tail, 10);
   nextline:
      tail = strchr(head, '\n');
      if (!tail)
         break;
      head = tail + 1;
   }
   if (!kb_low_total)
   { /* low==main except with large-memory support */
      kb_low_total = kb_main_total;
      kb_low_free = kb_main_free;
   }
   if (kb_inactive == ~0UL)
   {
      kb_inactive = kb_inact_dirty + kb_inact_clean + kb_inact_laundry;
   }
   kb_main_cached = kb_page_cache + kb_slab;
   kb_swap_used = kb_swap_total - kb_swap_free;
   kb_main_used = kb_main_total - kb_main_free - kb_main_cached - kb_main_buffers;

   /* zero? might need fallback for 2.6.27 <= kernel <? 3.14 */
   if (!kb_main_available)
   {
      if (linux_version_code < LINUX_VERSION(2, 6, 27))
         kb_main_available = kb_main_free;
      else
      {
         FILE_TO_BUF(VM_MIN_FREE_FILE, vm_min_free_fd);
         kb_min_free = (unsigned long)strtoull(buf, &tail, 10);

         watermark_low = kb_min_free * 5 / 4; /* should be equal to sum of all 'low' fields in /proc/zoneinfo */

         mem_available = (signed long)kb_main_free - watermark_low + kb_inactive_file + kb_active_file - MIN((kb_inactive_file + kb_active_file) / 2, watermark_low) + kb_slab_reclaimable - MIN(kb_slab_reclaimable / 2, watermark_low);

         if (mem_available < 0)
            mem_available = 0;
         kb_main_available = (unsigned long)mem_available;
      }
   }

   float pct_used = (float)kb_main_used * (100.0 / (float)kb_main_total);
   float pct_swap = kb_swap_total ? (float)kb_swap_used * (100.0 / (float)kb_swap_total) : 0;
#ifdef MEMGRAPH_OLD
   float pct_misc = (float)(kb_main_buffers + kb_main_cached) * (100.0 / (float)kb_main_total),
#else
   float pct_misc = (float)(kb_main_total - kb_main_available - kb_main_used) * (100.0 / (float)kb_main_total);
#endif

         printf("kb_main_used=%ld kb_main_total=%ld\n", kb_main_used, kb_main_total);
   printf("pct_used=%.1f\n", pct_used);
   printf("pct_swap=%.1f\n", pct_swap);
   printf("pct_misc1=%.1f\n", (float)(kb_main_buffers + kb_main_cached) * (100.0 / (float)kb_main_total));
   printf("pct_misc2=%.1f\n", (float)(kb_main_total - kb_main_available - kb_main_used) * (100.0 / (float)kb_main_total));
}
// end mem

/*
 * No matter what *they* say, we handle the really really BIG and
 * IMPORTANT stuff upon which all those lessor functions depend! */
static void before()
{
#define doALL STAT_REAP_NUMA_NODES_TOO
   int i, rc;
   int linux_version_code = procps_linux_version();

   // atexit(close_stdout);

   // setup our program name
   //   Myname = strrchr(me, '/');
   //  if (Myname) ++Myname; else Myname = me;

   // accommodate nls/gettext potential translations
   // ( must 'setlocale' before our libproc called )
   initialize_nls();

   // is /proc mounted?
   fatal_proc_unmounted(NULL, 0);

#ifndef OFF_STDERROR
   /* there's a chance that damn libnuma may spew to stderr so we gotta
      make sure he does not corrupt poor ol' top's first output screen!
      Yes, he provides some overridable 'weak' functions to change such
      behavior but we can't exploit that since we don't follow a normal
      ld route to symbol resolution (we use that dlopen() guy instead)! */
   // Stderr_save = dup(fileno(stderr));
   // if (-1 < Stderr_save && freopen("/dev/null", "w", stderr))
   //   ;                           // avoid -Wunused-result
#endif

   // establish some cpu particulars
   Hertz = procps_hertz_get();
   // Cpu_States_fmts = N_unq(STATE_lin2x6_fmt);
   // add 2024-6-22 kernel < 2.6.11 author:mr.liu
   /*
   if (linux_version_code < LINUX_VERSION(2, 6, 11))
   {
      //Cpu_States_fmts = N_unq(STATE_lin2x7_fmt);
       #define	__CYGWIN__  1
   }
   */

   // get the total cpus (and, if possible, numa node total)
   if ((rc = procps_stat_new(&Stat_ctx)))
      Restrict_some = Cpu_cnt = 1;
   else
   {
      if (!(Stat_reap = procps_stat_reap(Stat_ctx, doALL, Stat_items, MAXTBL(Stat_items))))
         // error_exit(fmtmk(N_fmt(LIB_errorcpu_fmt), __LINE__, strerror(errno)));
         perror("procps_stat_reap err!");
#ifndef PRETEND0NUMA
      Numa_node_tot = Stat_reap->numa->total;
#endif
      Cpu_cnt = Stat_reap->cpus->total;
#ifdef PRETEND48CPU
      Cpu_cnt = 48;
#endif
   }

   // prepare for memory stats from new library API ...
   if ((rc = procps_meminfo_new(&Mem_ctx)))
      Restrict_some = 1;

   // establish max depth for newlib pids stack (# of result structs)
   Pids_itms = alloc_c(sizeof(enum pids_item) * MAXTBL(Fieldstab));
   if (PIDS_noop != 0)
      for (i = 0; i < MAXTBL(Fieldstab); i++)
         Pids_itms[i] = PIDS_noop;
   Pids_itms_tot = MAXTBL(Fieldstab);
   // we will identify specific items in the build_headers() function
   if ((rc = procps_pids_new(&Pids_ctx, Pids_itms, Pids_itms_tot)))
      // error_exit(fmtmk(N_fmt(LIB_errorpid_fmt), __LINE__, strerror(-rc)));
      perror("procps_pids_new error!");

#if defined THREADED_CPU || defined THREADED_MEM || defined THREADED_TSK
   {
      struct sigaction sa;
      Thread_id_main = pthread_self();
      /* in case any of our threads have been enabled, they'll inherit this mask
         with everything blocked. therefore, signals go to the main thread (us). */
      sigfillset(&sa.sa_mask);
      pthread_sigmask(SIG_BLOCK, &sa.sa_mask, NULL);
   }
#endif

#ifdef THREADED_CPU
   if (0 != sem_init(&Semaphore_cpus_beg, 0, 0) || (0 != sem_init(&Semaphore_cpus_end, 0, 0)))
      if (0 != pthread_create(&Thread_id_cpus, NULL, cpus_refresh, NULL))
         pthread_setname_np(Thread_id_cpus, "update cpus");
#endif
#ifdef THREADED_MEM
   if (0 != sem_init(&Semaphore_memory_beg, 0, 0) || (0 != sem_init(&Semaphore_memory_end, 0, 0)))
      if (0 != pthread_create(&Thread_id_memory, NULL, memory_refresh, NULL))
         pthread_setname_np(Thread_id_memory, "update memory");
#endif
#ifdef THREADED_TSK
   if (0 != sem_init(&Semaphore_tasks_beg, 0, 0) || (0 != sem_init(&Semaphore_tasks_end, 0, 0)))
      if (0 != pthread_create(&Thread_id_tasks, NULL, tasks_refresh, NULL))
         pthread_setname_np(Thread_id_tasks, "update tasks");
#endif
   // lastly, establish support for graphing cpus & memory
   Graph_cpus = alloc_c(sizeof(struct graph_parms));
   Graph_mems = alloc_c(sizeof(struct graph_parms));
#undef doALL

   // don't distort startup cpu(s) display ...
   usleep(LIB_USLEEP);
} // end: before

int cpu_help(os_data *os, struct stat_stack *sta1, struct hist_tic *sta2)
{

   u_frme = sta1->head[stat_US].result.sl_int;        // user
   s_frme = sta1->head[stat_SY].result.sl_int;        // system
   n_frme = sta1->head[stat_NI].result.sl_int;        // nice
   i_frme = sta1->head[stat_IL].result.sl_int;        // idle
   w_frme = sta1->head[stat_IO].result.sl_int;        // iowait
   x_frme = sta1->head[stat_IR].result.sl_int;        // hard irq
   y_frme = sta1->head[stat_SI].result.sl_int;        // soft irq
   z_frme = sta1->head[stat_ST].result.sl_int;        // steal
   z1_frme = sta1->head[stat_GU].result.sl_int;       // guest
   z2_frme = sta1->head[stat_GN].result.sl_int;       // guest_nic
   tot_frme = sta1->head[stat_SUM_TOT].result.sl_int; // sum_tot
   sum_user = sta1->head[stat_SUM_USR].result.sl_int; // sum_user
   sum_sys = sta1->head[stat_SUM_SYS].result.sl_int;  // sum_system
   // new style
   s_frme += z1_frme + z2_frme;
   sum_sys += z1_frme + z2_frme;
   float scale1 = 0.0;
   u_f = sta2->new.user;
   if (1 > tot_frme)
      i_frme = tot_frme = 1;
   scale = 100.0 / (float)tot_frme;
   // add 2024-8-21
   // char s1[6],s2[6],s3[6],s4[6],s5[6],s6[6],s7[6],s8[6],s9[6],s10[6],s11[6],s12[6],s13[6];
   snprintf(os->cpu_p.u_frme_p, 6, "%.1f", (float)round(u_frme * scale * 10) / 10);
   snprintf(os->cpu_p.s_frme_p, 6, "%.1f", (float)round(s_frme * scale * 10) / 10);
   snprintf(os->cpu_p.n_frme_p, 6, "%.1f", (float)round(n_frme * scale * 10) / 10);
   snprintf(os->cpu_p.i_frme_p, 6, "%.1f", (float)round(i_frme * scale * 10) / 10);
   snprintf(os->cpu_p.w_frme_p, 6, "%.1f", (float)round(w_frme * scale * 10) / 10);
   snprintf(os->cpu_p.x_frme_p, 6, "%.1f", (float)round(x_frme * scale * 10) / 10);
   snprintf(os->cpu_p.y_frme_p, 6, "%.1f", (float)round(y_frme * scale * 10) / 10);
   snprintf(os->cpu_p.z_frme_p, 6, "%.1f", (float)round(z_frme * scale * 10) / 10);
   snprintf(os->cpu_p.z1_frme_p, 6, "%.1f", (float)round(z1_frme * scale * 10) / 10);
   snprintf(os->cpu_p.z2_frme_p, 6, "%.1f", (float)round(z2_frme * scale * 10) / 10);
   snprintf(os->cpu_p.sum_user_p, 6, "%.1f", (float)round(sum_user * scale * 10) / 10);
   snprintf(os->cpu_p.sum_sys_p, 6, "%.1f", (float)round(sum_sys * scale * 10) / 10);
   float puser = (float)round(sum_user * scale * 10) / 10;
   float psys = (float)round(sum_sys * scale * 10) / 10;
   if (puser + psys > 100.0 || psys < 0)
      psys = 0;
   snprintf(os->cpu_p.sum_total_p, 6, "%.1f", ((puser + psys) * 10) / 10);

   os->cpu_p.u_frme = u_frme;
   os->cpu_p.u_f = u_f;
   /*
        strcpy(os->cpu_p.u_frme_p, s1);
        strcpy(os->cpu_p.s_frme_p, s2);
        strcpy(os->cpu_p.n_frme_p, s3);
        strcpy(os->cpu_p.i_frme_p, s4);
        strcpy(os->cpu_p.w_frme_p, s5);
        strcpy(os->cpu_p.x_frme_p, s6);
        strcpy(os->cpu_p.y_frme_p, s7);
        strcpy(os->cpu_p.z_frme_p, s8);
        strcpy(os->cpu_p.z1_frme_p, s9);
        strcpy(os->cpu_p.z2_frme_p, s10);
        strcpy(os->cpu_p.sum_user_p, s11);
        strcpy(os->cpu_p.sum_sys_p, s12);
        strcpy(os->cpu_p.sum_total_p, s13);
   */
}

/*
 * This serves as our interface to the memory portion of libprocps.
 * The sampling frequency is reduced in order to minimize overhead. */
static void *memory_refresh(void *unused)
{
   static time_t sav_secs;
   time_t cur_secs;

   do
   {
#ifdef THREADED_MEM
      sem_wait(&Semaphore_memory_beg);
#endif
      // if (Frames_signal)
      //   sav_secs = 0;
      cur_secs = time(NULL);

      if (3 <= cur_secs - sav_secs)
      {
         if (!(Mem_stack = procps_meminfo_select(Mem_ctx, Mem_items, MAXTBL(Mem_items))))
            // error_exit(fmtmk(N_fmt(LIB_errormem_fmt), __LINE__, strerror(errno)));
            perror("procps_meminfo_select failed");
         sav_secs = cur_secs;
      }
#ifdef THREADED_MEM
      sem_post(&Semaphore_memory_end);
   } while (1);
#else
   } while (0);
#endif
   return NULL;
   (void)unused;
} // end: memory_refresh

int mem_help(struct meminfo_stack *this, os_data *that)
{
   //	that->meminfo.KB_active=this->hist.new.Active;

   //	that->meminfo.KB_main_buffers=this->hist.new.Buffers;
   that->meminfo.KB_main_buffers = this->head[mem_BUF].result.ul_int;

   //	that->meminfo.KB_page_cached=this->hist.new.Cached;
   //	that->meminfo.KB_inactive=this->hist.new.Inactive;

   // that->meminfo.KB_main_available=this->hist.new.MemAvailable;
   that->meminfo.KB_main_available = this->head[mem_AVL].result.ul_int;

   // that->meminfo.KB_main_free=this->hist.new.MemFree;
   that->meminfo.KB_main_free = this->head[mem_FRE].result.ul_int;

   // that->meminfo.KB_main_total=this->hist.new.MemTotal;
   that->meminfo.KB_main_total = this->head[mem_TOT].result.ul_int;
   mem_TOT_global = this->head[mem_TOT].result.ul_int;

   // that->meminfo.KB_swap_free=this->hist.new.SwapFree;
   that->meminfo.KB_swap_free = this->head[swp_FRE].result.ul_int;

   // that->meminfo.KB_swap_total=this->hist.new.SwapTotal;
   that->meminfo.KB_swap_total = this->head[swp_TOT].result.ul_int;

   // that->meminfo.KB_main_cached=this->hist.new.derived_mem_cached;
   that->meminfo.KB_main_cached = this->head[mem_QUE].result.ul_int;

   // that->meminfo.KB_swap_used=this->hist.new.derived_swap_used;
   that->meminfo.KB_swap_used = this->head[swp_USE].result.ul_int;

   // that->meminfo.KB_main_used=this->hist.new.derived_mem_used;
   that->meminfo.KB_main_used = this->head[mem_USE].result.ul_int;

   long my_qued, my_misc, my_used;
   // add 2024-8-21 update
   //	char m1[6],m2[6];
   float scale = 0.0;
   my_qued = this->head[mem_BUF].result.ul_int + this->head[mem_QUE].result.ul_int;
   my_used = this->head[mem_TOT].result.ul_int - this->head[mem_FRE].result.ul_int - my_qued;
   my_misc = this->head[mem_TOT].result.ul_int - this->head[mem_AVL].result.ul_int - my_used;
   scale = 100.0 / this->head[mem_TOT].result.ul_int;
   float p_used = (float)round(my_used * scale * 10) / 10;
   float p_misc = (float)round(my_misc * scale * 10) / 10;

   if (p_used + p_misc > 100.0 || p_misc < 0)
      p_misc = 0;
   snprintf(that->meminfo.pcnt_tot, 6, "%.1f", ((p_used + p_misc) * 10) / 10);
   // strcpy(that->meminfo.pcnt_tot, m1);
   float p_swap = (float)round(this->head[swp_USE].result.ul_int * 100.0 / this->head[swp_TOT].result.ul_int * 10) / 10;
   snprintf(that->meminfo.swp_USE_p, 6, "%.1f", p_swap);
   // strcpy(that->meminfo.swp_USE_p, m2);
}

// process

/*
 * Set up the raw/incomplete field group windows --
 * they'll be finished off after startup completes.
 * [ and very likely that will override most/all of our efforts ]
 * [               --- life-is-NOT-fair ---                     ] */
static void wins_stage_1(void)
{
   WIN_t *w;
   int i;

   for (i = 0; i < GROUPSMAX; i++)
   {
      w = &Winstk[i];
      w->winnum = i + 1;
      w->rc = Rc.win[i];
      w->captab[0] = Cap_norm;
      w->captab[1] = Cap_norm;
      w->captab[2] = w->cap_bold;
      w->captab[3] = w->capclr_sum;
      w->captab[4] = w->capclr_msg;
      w->captab[5] = w->capclr_pmt;
      w->captab[6] = w->capclr_hdr;
      w->captab[7] = w->capclr_rowhigh;
      w->captab[8] = w->capclr_rownorm;
      w->next = w + 1;
      w->prev = w - 1;
   }

   // fixup the circular chains...
   Winstk[GROUPSMAX - 1].next = &Winstk[0];
   Winstk[0].prev = &Winstk[GROUPSMAX - 1];
   Curwin = Winstk;

   for (i = 1; i < BOT_MSGSMAX; i++)
      Msg_tab[i].prev = &Msg_tab[i - 1];
   Msg_tab[0].prev = &Msg_tab[BOT_MSGSMAX - 1];
} // end: wins_stage_1

/*
static void whack_terminal (void) {
   static char dummy[] = "dumb";
   struct termios tmptty;

   // the curses part...
   if (Batch) {
      setupterm(dummy, STDOUT_FILENO, NULL);
      return;
   }
#ifdef PRETENDNOCAP
   setupterm(dummy, STDOUT_FILENO, NULL);
#else
   setupterm(NULL, STDOUT_FILENO, NULL);
#endif
   // our part...
   if (-1 == tcgetattr(STDIN_FILENO, &Tty_original))
   // ok, haven't really changed anything but we do have our snapshot
   Ttychanged = 1;

   // first, a consistent canonical mode for interactive line input
   tmptty = Tty_original;
   tmptty.c_lflag |= (ECHO |  ECHOE | ICANON | ISIG);
   tmptty.c_lflag &= ~NOFLSH;
   //tmptty.c_oflag &= ~TAB3;
   tmptty.c_iflag |= BRKINT;
   tmptty.c_iflag &= ~IGNBRK;
#ifdef TERMIOS_ONLY
   if (-1 == tcsetattr(STDIN_FILENO, TCSAFLUSH, &tmptty))
   tcgetattr(STDIN_FILENO, &Tty_tweaked);
#endif
   // lastly, a nearly raw mode for unsolicited single keystrokes
   tmptty.c_lflag &= ~(ECHO | ECHOE | ICANON);
   tmptty.c_cc[VMIN] = 1;
   tmptty.c_cc[VTIME] = 0;
   if (-1 == tcsetattr(STDIN_FILENO, TCSAFLUSH, &tmptty))
   tcgetattr(STDIN_FILENO, &Tty_raw);

#ifndef OFF_STDIOLBF
   // thanks anyway stdio, but we'll manage buffering at the frame level...
   setbuffer(stdout, Stdout_buf, sizeof(Stdout_buf));
#endif
#ifdef OFF_SCROLLBK
   // this has the effect of disabling any troublesome scrollback buffer...
   if (enter_ca_mode) putp(enter_ca_mode);
#endif
   // and don't forget to ask iokey to initialize his tinfo_tab
   //iokey(IOKEY_INIT);
} // end: whack_terminal
*/

/*
 * This guy just completes the field group windows after the
 * rcfiles have been read and command line arguments parsed.
 * And since he's the cabose of startup, he'll also tidy up
 * a few final things... */
/*
static void wins_stage_2 (void) {
   int i;

   for (i = 0; i < GROUPSMAX; i++) {
      win_names(&Winstk[i], Winstk[i].rc.winname);
      capsmk(&Winstk[i]);
      Winstk[i].findstr = alloc_c(FNDBUFSIZ);
      Winstk[i].findlen = 0;
      if (Winstk[i].rc.combine_cpus >= Cpu_cnt)
         Winstk[i].rc.combine_cpus = 0;
      if (CHKw(&Winstk[i], (View_CPUSUM | View_CPUNOD)))
         Winstk[i].rc.double_up = 0;
   }
   if (!Batch)
      putp((Cursor_state = Cap_curs_hide));
   else {
      OFFw(Curwin, View_SCROLL);
      //signal(SIGHUP, SIG_IGN);    // allow running under nohup
   }
   // fill in missing Fieldstab members and build each window's columnhdr
   zap_fieldstab();

   // with preserved 'other filters' & command line 'user filters',
   // we must ensure that we always have a visible task on row one.
   mkVIZrow1

   // lastly, initialize a signal set used to throttle one troublesome signal
   sigemptyset(&Sigwinch_set);
#ifdef SIGNALS_LESS
   sigaddset(&Sigwinch_set, SIGWINCH);
#endif
} // end: wins_stage_2
*/

static void *tasks_refresh(void *unused)
{
#define nALIGN(n, m) (((n + m - 1) / m) * m)  // unconditionally align
#define nALGN2(n, m) ((n + m - 1) & ~(m - 1)) // with power of 2 align
#define n_reap Pids_reap->counts->total
   static double uptime_sav;
   static int n_alloc = -1; // size of windows stacks arrays
   double uptime_cur;
   float et;
   int i, what;

   do
   {
#ifdef THREADED_TSK
      sem_wait(&Semaphore_tasks_beg);
#endif
      procps_uptime(&uptime_cur, NULL);
      et = uptime_cur - uptime_sav;
      if (et < 0.01)
         et = 0.005;
      uptime_sav = uptime_cur;
      // if in Solaris mode, adjust our scaling for all cpus
      Frame_etscale = 100.0f / ((float)Hertz * (float)et * (Rc.mode_irixps ? 1 : Cpu_cnt));

      what = Thread_mode ? PIDS_FETCH_THREADS_TOO : PIDS_FETCH_TASKS_ONLY;
      if (Monpidsidx)
      {
         what |= PIDS_SELECT_PID;
         Pids_reap = procps_pids_select(Pids_ctx, (unsigned *)Monpids, Monpidsidx, what);
      }
      else
         Pids_reap = procps_pids_reap(Pids_ctx, what);
      if (!Pids_reap)
         perror("Pids_reap error");
      // now refresh each window's stacks pointer array...
      /*
            if (n_alloc < n_reap) {
      //       n_alloc = nALIGN(n_reap, 100);
               n_alloc = nALGN2(n_reap, 128);
               for (i = 0; i < GROUPSMAX; i++) {
                  Winstk[i].ppt = alloc_r(Winstk[i].ppt, sizeof(void *) * n_alloc);
                  memcpy(Winstk[i].ppt, Pids_reap->stacks, sizeof(void *) * PIDSmaxt);
               }
            } else {
               for (i = 0; i < GROUPSMAX; i++)
                  memcpy(Winstk[i].ppt, Pids_reap->stacks, sizeof(void *) * PIDSmaxt);
            }
            //set printf
      */

#ifdef THREADED_TSK
      sem_post(&Semaphore_tasks_end);
   } while (1);
#else
   } while (0);
#endif
   return NULL;
   (void)unused;
#undef nALIGN
#undef nALGN2
#undef n_reap
} // end: tasks_refresh

static void *tasks_refresh1(void *unused)
{
#define nALIGN(n, m) (((n + m - 1) / m) * m)  // unconditionally align
#define nALGN2(n, m) ((n + m - 1) & ~(m - 1)) // with power of 2 align
#define n_reap Pids_reap1->counts->total
   static double uptime_sav1;
   static int n_alloc = -1; // size of windows stacks arrays
   double uptime_cur1;
   float et;
   int i, what;

   do
   {
#ifdef THREADED_TSK
      sem_wait(&Semaphore_tasks_beg);
#endif
      procps_uptime(&uptime_cur1, NULL);
      et = uptime_cur1 - uptime_sav1;
      if (et < 0.01)
         et = 0.005;
      uptime_sav1 = uptime_cur1;
      // if in Solaris mode, adjust our scaling for all cpus
      Frame_etscale = 100.0f / ((float)Hertz * (float)et * (Rc.mode_irixps ? 1 : Cpu_cnt));

      what = Thread_mode ? PIDS_FETCH_THREADS_TOO : PIDS_FETCH_TASKS_ONLY;
      if (Monpidsidx)
      {
         what |= PIDS_SELECT_PID;
         Pids_reap1 = procps_pids_select(Pids_ctx, (unsigned *)Monpids, Monpidsidx, what);
      }
      else
         Pids_reap1 = procps_pids_reap1(Pids_ctx, what);
      if (!Pids_reap1)
         perror("Pids_reap error");
      // now refresh each window's stacks pointer array...
      // update 2024
      /*
            if (n_alloc < n_reap) {
      //       n_alloc = nALIGN(n_reap, 100);
               n_alloc = nALGN2(n_reap, 128);
               for (i = 0; i < GROUPSMAX; i++) {
                  Winstk[i].ppt = alloc_r(Winstk[i].ppt, sizeof(void *) * n_alloc);
                  memcpy(Winstk[i].ppt, Pids_reap1->stacks, sizeof(void *) * PIDSmaxt);
               }
            } else {
               for (i = 0; i < GROUPSMAX; i++)
                  memcpy(Winstk[i].ppt, Pids_reap1->stacks, sizeof(void *) * PIDSmaxt);
            }
            //set printf
      */

#ifdef THREADED_TSK
      sem_post(&Semaphore_tasks_end);
   } while (1);
#else
   } while (0);
#endif
   return NULL;
   (void)unused;
#undef nALIGN
#undef nALGN2
#undef n_reap
} // end: tasks_refresh1

// init_cpu
int init_cpu()
{

   before();
   mem_TOT_global = (long)Mem_ctx->hist.new.MemTotal;
   //      wins_stage_1();
   //     whack_terminal();
   //    wins_stage_2();

   in_cpu = 1;
}

// end init_cpu
/*
 * This little recursive guy was the real forest view workhorse. |
 * He fills in the Tree_ppt array and also sets the child indent |
 * level which is stored in an 'extra' result struct as a u_int. | */
static void forest_adds(const int self, int level)
{
   // tailored 'results stack value' extractor macros
#define rSv(E, X) PID_VAL(E, s_int, Seed_ppt[X])
   // if xtra-procps-debug.h active, can't use PID_VAL with assignment
#define rSv_Lvl Tree_ppt[Tree_idx]->head[eu_TREE_LVL].result.s_int
   int i;

   if (Tree_idx < PIDSmaxt)
   { // immunize against insanity |
      if (level > 100)
         level = 101;                      // our arbitrary nests limit |
      Tree_ppt[Tree_idx] = Seed_ppt[self]; // add this as root or child |
      rSv_Lvl = level;                     // while recording its level |
      ++Tree_idx;
#ifdef TREE_SCANALL
      for (i = 0; i < PIDSmaxt; i++)
      {
         if (i == self)
            continue;
#else
      for (i = self + 1; i < PIDSmaxt; i++)
      {
#endif
         if (rSv(EU_PID, self) == rSv(EU_TGD, i) || (rSv(EU_PID, self) == rSv(EU_PPD, i) && rSv(EU_PID, i) == rSv(EU_TGD, i)))
            forest_adds(i, level + 1); // got one child any others?
      }
   }
#undef rSv
#undef rSv_Lvl
} // end: forest_adds

/*
 * This function is responsible for making that stacks ptr array |
 * a forest display in that designated window. After completion, |
 * he'll replace that original window ppt array with a specially |
 * ordered forest view version. He'll also mark hidden children! | */
static void forest_begin(WIN_t *q)
{
   static int hwmsav;
   int i, j;

   Seed_ppt = q->ppt; // avoid passing pointers |
   if (!Tree_idx)
   { // do just once per frame |
      if (hwmsav < PIDSmaxt)
      { // grow, but never shrink |
         hwmsav = PIDSmaxt;
         Tree_ppt = alloc_r(Tree_ppt, sizeof(void *) * hwmsav);
      }

#ifndef TREE_SCANALL
      if (!(procps_pids_sort(Pids_ctx, Seed_ppt, PIDSmaxt, PIDS_TICS_BEGAN, PIDS_SORT_ASCEND)))
      // error_exit(fmtmk(N_fmt(LIB_errorpid_fmt), __LINE__, strerror(errno)));
#endif
         for (i = 0; i < PIDSmaxt; i++)
         {                                                 // avoid hidepid distorts |
            if (!PID_VAL(eu_TREE_LVL, s_int, Seed_ppt[i])) // parents lvl 0 |
               forest_adds(i, 0);                          // add parents + children |
         }

      /* we use up to three additional 'PIDS_extra' results in our stack |
            eu_TREE_HID (s_ch) :  where 'x' == collapsed & 'z' == unseen |
            eu_TREE_LVL (s_int):  where level number is stored (0 - 100) |
            eu_TREE_ADD (u_int):  where a children's tics stored (maybe) | */
      for (i = 0; i < Hide_tot; i++)
      {

         // if have xtra-procps-debug.h, cannot use PID_VAL w/ assignment |
#define rSv(E, T, X) Tree_ppt[X]->head[E].result.T
#define rSv_Pid(X) rSv(EU_PID, s_int, X)
#define rSv_Lvl(X) rSv(eu_TREE_LVL, s_int, X)
#define rSv_Hid(X) rSv(eu_TREE_HID, s_ch, X)
         /* next 2 aren't needed if TREE_VCPUOFF but they cost us nothing |
            & the EU_CPU slot will now always be present (even if it's 0) | */
#define rSv_Add(X) rSv(eu_TREE_ADD, u_int, X)
#define rSv_Cpu(X) rSv(EU_CPU, u_int, X)

         if (Hide_pid[i] > 0)
         {
            for (j = 0; j < PIDSmaxt; j++)
            {
               if (rSv_Pid(j) == Hide_pid[i])
               {
                  int parent = j;
                  int children = 0;
                  int level = rSv_Lvl(parent);
                  while (j + 1 < PIDSmaxt && rSv_Lvl(j + 1) > level)
                  {
                     ++j;
                     rSv_Hid(j) = 'z';
#ifndef TREE_VCPUOFF
                     rSv_Add(parent) += rSv_Cpu(j);
#endif
                     children = 1;
                  }
                  /* if any children found (& collapsed) mark the parent |
                     ( when children aren't found don't negate the pid ) |
                     ( to prevent future scans since who's to say such ) |
                     ( tasks will not fork more children in the future ) | */
                  if (children)
                     rSv_Hid(parent) = 'x';
                  // this will force a check of next Hide_pid[i], if any |
                  j = PIDSmaxt + 1;
               }
            }
            // if a target task disappeared prevent any further scanning |
            if (j == PIDSmaxt)
               Hide_pid[i] = -Hide_pid[i];
         }
#undef rSv
#undef rSv_Pid
#undef rSv_Lvl
#undef rSv_Hid
#undef rSv_Add
#undef rSv_Cpu
      }
   } // end: !Tree_idx
   memcpy(Seed_ppt, Tree_ppt, sizeof(void *) * PIDSmaxt);
} // end: forest_begin

char *cal_cpu(struct pids_stack *p)
{
#define rSv(E, T) PID_VAL(E, T, p)
   float u = (float)rSv(EU_CPU, u_int);
   int n = rSv(EU_THD, s_int);
   u *= Frame_etscale;
   /* process can't use more %cpu than number of threads it has
    ( thanks Jaromir Capik <<EMAIL>> ) */
   if (u > 100.0 * n)
      u = 100.0 * n;
   // cpu% not allow to exceed cpu counts
   Cpu_pmax = 100.0 * Cpu_cnt;
   if (u > Cpu_pmax)
      u = Cpu_pmax;
   //	     static char buf[SMLBUFSIZ];
   char *buf = calloc(1, 7);

   snprintf(buf, sizeof(buf), "%#.1f", u);

   return buf;
}

char *cal_time(struct pids_stack *p)
{
#define rSv(E, T) PID_VAL(E, T, p)
   //		 printf("Frame_etscale=%.1f CPU_TIME=%lu cmd=%s\n",Frame_etscale,rSv(EU_CPU,u_int),rSv(EU_CMD,str));
   //		 fflush(stdout);
   float t = (float)rSv(EU_TM2, ull_int);
   //            cp = scale_tics(t, W, Jn, TICS_AS_SECS);
   float nt;
   nt = (t * 100ull) / Hertz;
   int rs = 12;
   // re:
   char *ti = calloc(1, rs);

   snprintf(ti, rs, "%.2f", (float)round(nt / 100.00 / 60.00 * 100) / 100);
   /*
   char c='.';
   if (!*(strchr(ti,c)+1) )
   {
      rs++;
      free(ti);
      ti=NULL;
      goto re;

   }
   */
   // nt /=100;
   //	int t1 =(int) round( nt/60);

   return ti;
}

char *cal_mem(struct pids_stack *p)
{
#define rSv(E, T) PID_VAL(E, T, p)
   //      cp = scale_pcnt((float)rSv(EU_MEM, ul_int) * 100 / MEM_VAL(mem_TOT), W, Jn, 0);
   float a1 = (float)rSv(EU_RES, ul_int);
   // float a2=(float) MEM_VAL(mem_TOT);
   float a2 = Mem_ctx->hist.new.MemTotal;
   float m;
   m = a1 * 100.0 / a2;
   char *buf = calloc(1, 7);

   snprintf(buf, sizeof(buf), "%#.1f", m);

   return buf;
}

int init_parpids(os_data *oss, struct pids_stack *pp, char *c, char *t, char *m)
{
   oss->process_pid.PID = pp->head[EU_PID].result.s_int;
   oss->process_pid.TGID = pp->head[EU_TGD].result.s_int;
   // add 2024-8-21 update
   // strcpy(oss->process_pid.USER,pp->head[EU_UEN].result.str);
   snprintf(oss->process_pid.USER, 10, "%s", pp->head[EU_UEN].result.str);

   oss->process_pid.PR = pp->head[EU_PRI].result.s_int;
   oss->process_pid.NI = pp->head[EU_NCE].result.s_int;
   oss->process_pid.VIRT = pp->head[EU_VRT].result.ul_int;
   oss->process_pid.RES = pp->head[EU_RES].result.ul_int;
   oss->process_pid.SHR = pp->head[EU_SHR].result.ul_int;
   // strcpy(os->process_pid.S,p->head[EU_STA].result.s_ch);
   oss->process_pid.S = pp->head[EU_STA].result.s_ch;
   oss->process_pid.threads = pp->head[EU_THD].result.s_int - 1;

   // add 2024-8-21 update
   // strcpy(oss->process_pid.CPU_P,c);
   snprintf(oss->process_pid.CPU_P, 6, "%s", c);
   // strcpy(oss->process_pid.MEM_P,m);
   snprintf(oss->process_pid.MEM_P, 6, "%s", m);
   // strcpy(oss->process_pid.CPU_TIME,t);
   snprintf(oss->process_pid.CPU_TIME, 12, "%s", t);
   // strcpy(oss->process_pid.COMMAND,pp->head[EU_CMD].result.str);
   snprintf(oss->process_pid.COMMAND, 50, "%s", pp->head[EU_CMD].result.str);
   // free(pp->head[EU_CMD].result.str);
   free(m);
   free(c);
   free(t);
   m = NULL;
   c = NULL;
   t = NULL;
}

int init_thrpids(os_data *os, struct pids_stack *p, char *c, char *t, char *m)
{
   //            os->thread_pid.PPID=p->head[EU_PPD].result.s_int;
   os->thread_pid.TGID = p->head[EU_TGD].result.s_int;

   os->thread_pid.PID = p->head[EU_PID].result.s_int;
   // add 2024-8-21 update
   // strcpy(os->thread_pid.USER,p->head[EU_UEN].result.str);
   snprintf(os->thread_pid.USER, 10, "%s", p->head[EU_UEN].result.str);
   os->thread_pid.PR = p->head[EU_PRI].result.s_int;
   os->thread_pid.NI = p->head[EU_NCE].result.s_int;
   os->thread_pid.VIRT = p->head[EU_VRT].result.ul_int;
   os->thread_pid.RES = p->head[EU_RES].result.ul_int;
   os->thread_pid.SHR = p->head[EU_SHR].result.ul_int;
   // strcpy(os->thread_pid.S,p->head[EU_STA].result.s_ch);
   os->thread_pid.S = p->head[EU_STA].result.s_ch;
   // add 2024-8-21 update
   //  strcpy(os->thread_pid.CPU_P,c);
   snprintf(os->thread_pid.CPU_P, 6, "%s", c);
   // strcpy(os->thread_pid.MEM_P,m);
   snprintf(os->thread_pid.MEM_P, 6, "%s", m);
   // strcpy(os->thread_pid.CPU_TIME,t);
   snprintf(os->thread_pid.CPU_TIME, 12, "%s", t);
   // strcpy(os->thread_pid.COMMAND,p->head[EU_CMD].result.str);
   snprintf(os->thread_pid.COMMAND, 50, "%s", p->head[EU_CMD].result.str);

   // free(p->head[EU_CMD].result.str);
   free(m);
   free(c);
   free(t);

   m = NULL;
   c = NULL;
   t = NULL;
}

int add_process_thread_count(os_data *os)
{

   os->process_thread_count = PIDSmaxt;
   os->process_thread_running_count = PIDSRUN;
   os->process_thread_sleeping_count = Pids_reap->counts->sleeping + Pids_reap->counts->other;
   os->process_thread_stopped_count = Pids_reap->counts->stopped;
   os->process_thread_zombie_count = Pids_reap->counts->zombied;
}

int add_thread_count(os_data *os)
{

   os->thread_count = PIDSmaxt1;
   os->thread_running_count = PIDSRUN1;
   os->thread_sleeping_count = Pids_reap1->counts->sleeping + Pids_reap->counts->other;
   os->thread_stopped_count = Pids_reap1->counts->stopped;
   os->thread_zombie_count = Pids_reap1->counts->zombied;
}

/*
void recursive_lock(pthread_mutex_t *mutex, pthread_cond_t *cond) {
    pthread_mutex_lock(mutex);
    while (lock_count != 0 && owner != pthread_self()) {
        pthread_cond_wait(cond, mutex);
    }
    owner = pthread_self();
    lock_count++;
}

void recursive_unlock(pthread_mutex_t *mutex, pthread_cond_t *cond) {
    lock_count--;
    if (lock_count == 0) {
        owner = 0;
        pthread_cond_broadcast(cond);
    }
    pthread_mutex_unlock(mutex);
}
*/

void usSleep(int nUs)
{
   struct timeval begin;
   struct timeval now;
   int pastUs = 0;
   gettimeofday(&begin, NULL);
   now.tv_sec = begin.tv_sec;
   now.tv_usec = begin.tv_usec;
   while (pastUs < nUs)
   {
      gettimeofday(&now, NULL);
      pastUs = (now.tv_sec - begin.tv_sec) * 1000000 - begin.tv_usec + now.tv_usec;
   }
}

/**
 * @brief 等待指定的信号量，处理 EINTR 中断和错误。
 *
 * @param sem 指向要等待的信号量的指针。
 */
static void wait_for_semaphore(sem_t *sem) {
    int ret;
    while ((ret = sem_wait(sem)) == -1) {
        if (errno == EINTR) {
            // 被信号中断，继续等待
            continue;
        } else {
            // 发生其他错误
            perror("sem_wait failed in wait_for_semaphore");
            // 根据你的错误处理策略，可以选择 exit 或返回错误码
            exit(EXIT_FAILURE);
        }
    }
    // sem_wait 成功返回
}

int sys()
{

   aaa = 0;
   if (in_cpu == 0)
   {
      init_cpu();
      aaa = 32768; // not jump for
   }
  /*  get_load();
   if (load1_old == -1 && load5_old == -1 && load15_old == -1)
   {
      load1_old = round(loads1 / 32);
      load5_old = round(loads2 / 32);
      load15_old = round(loads3 / 32);
   } */
       sleep(5);
   //usSleep(5000000);//太占用cpu

   /* float load1_new = round(loads1 / 32);
   float load5_new = round(loads2 / 32);
   float load15_new = round(loads3 / 32);
   if (active1 * 2048 >= load1_old)
   {
      active1 = (((load1_new * 2048 - 2047) - load1_old * 1884) / 164) / 2048;
      active1 = (active1 < 0) ? -active1 : active1;
   }
   else
   {
      active1 = (((load1_new * 2048) - load1_old * 1884) / 164) / 2048;
      active1 = (active1 < 0) ? -active1 : active1;
   } */
   //    unsigned int active1_1=round((round(((load1_new*2048-2047)-load1_old*1884)/164))/2048);
   //   unsigned int active5=(((load5_new*2048-2047)-load5_old*2014)/34)/2048;
   //  unsigned int active15=(((load15_new*2048-2047)-load15_old*2037)/11)/2048;

   // initialize struct
   //	os= alloc_c((Cpu_cnt+1) * sizeof(os_data));
   // os= calloc(1,1000 * sizeof(os_data));
   os_data *os_sum = &os[Cpu_cnt];
   // 返回的字符指针转换为字符数组
   // strcpy(os->avg_load11, avg_load());
   //avg_load(os_sum);
   // strcpy(os->time1, get_time());
   //get_time1(os_sum);
   // 对active1 四舍五入
   //int result = round(active1);
   //os_sum->active1 = result;
   // hostname set
   gethostname(os->hostname, sizeof(os->hostname));

   //	v4.0.4
   os->cores = Cpu_cnt;
   cpus_refresh(NULL);
   /*
   printf("histotry result:new.user=%llu old.user=%llu\n",Stat_ctx->cpu_hist.new.user,Stat_ctx->cpu_hist.old.user);
        printf("idle=%lld item=%d\n",Stat_reap->summary->head[stat_IL].result.sl_int,Stat_reap->summary->head[stat_IL].item);
         printf("user=%lld item=%d\n",Stat_reap->summary->head[stat_US].result.sl_int,Stat_reap->summary->head[stat_US].item);
   printf("sum_user=%lld item=%d\n",Stat_reap->summary->head[stat_SUM_USR].result.sl_int,Stat_reap->summary->head[stat_SUM_USR].item);
   printf("sum_sys=%lld item=%d\n",Stat_reap->summary->head[stat_SUM_SYS].result.sl_int,Stat_reap->summary->head[stat_SUM_SYS].item);

   */
   cpu_help(os_sum, Stat_reap->summary, &Stat_ctx->cpu_hist);
   int i;
   for (i = 0; i < Cpu_cnt; i++)
   {
      /*
                   printf("cpu%d_idle=%lld item=%d\n",i,Stat_reap->cpus->stacks[i]->head[stat_IL].result.sl_int,Stat_reap->cpus->stacks[i]->head[stat_IL].item);
      */
      os_data *os_cpus = &os[i];
      struct hist_tic *his = &Stat_ctx->cpus.hist.tics[i];

      cpu_help(os_cpus, Stat_reap->cpus->stacks[i], his);
   }
   // mem
   /*
      printf("###mem info####\n");
      printf("free_mem=%lu\n",Mem_ctx->hist.new.MemFree);
      printf("total_mem=%lu\n",Mem_ctx->hist.new.MemTotal);
      printf("###############\n");
   */
  
   memory_refresh(NULL);
   mem_help(Mem_stack, os_sum);

   // process - 根据enable_tasks_refresh控制是否执行进程和线程相关操作
   int count = 0;  // 将count变量声明移到条件判断之前

   if (enable_tasks_refresh) {
      int pp;
      Thread_mode = 0;
      if (tip == 0)
      {
         Thread_mode = 1;
         tasks_refresh(NULL);
         for (pp = 0; pp < PIDSmaxt; pp++)
         {
            if (Pids_reap->stacks[pp]->head[EU_CMD].result.str != NULL)
            {

               free(Pids_reap->stacks[pp]->head[EU_CMD].result.str);
            }
         }

         tip = 1;
      }
      Thread_mode = 1;
      tasks_refresh(NULL);

      // 添加进程和线程计数
      add_process_thread_count(&os[0]);
      // 添加线程计数
      //add_thread_count(&os[0]);

      // init active process first
      o = 0;
      o1 = 0; // 线程计数器初始化
      int p1, p2;
      // realloc struct
      if (Cpu_cnt + 1 < PIDSRUN)
      {
         os = alloc_r(os, PIDSRUN * sizeof(os_data));
         if (os == NULL) {
            perror("内存分配失败");
            return;
         }
         memset(os + Cpu_cnt + 1, 0, (PIDSRUN - Cpu_cnt - 1) * sizeof(os_data));
         count = PIDSRUN;
      }
      else
      {
         count = Cpu_cnt + 1;
      }

      os_data *first = &os[0];

      // 使用单次循环同时处理进程和线程
      for (p1 = 0; p1 < PIDSmaxt; p1++)
      {
         // 确保索引在有效范围内
         if (p1 < 0 || p1 >= PIDSmaxt || Pids_reap->stacks[p1] == NULL) {
            continue;
         }

         int pid = Pids_reap->stacks[p1]->head[EU_PID].result.s_int;
         int tgid = Pids_reap->stacks[p1]->head[EU_TGD].result.s_int;
         int c1 = Pids_reap->stacks[p1]->head[EU_CPU].result.u_int;
         char *cmd = Pids_reap->stacks[p1]->head[EU_CMD].result.str;

         // 检查cmd是否为"check_thread"，如果是，打印c1的值,还有tgid
         if (cmd != NULL && strcmp(cmd, "check_thread") == 0) {
           // write_log("检测到check_thread进程，c1值为：%d, tgid值为：%d\n", c1, tgid);
         }

         // 打印所有进程和线程的cmd和c1值
         if (cmd != NULL) {
           // write_log("进程/线程命令: %s, c1值: %d, PID: %d, TGID: %d\n", cmd, c1, pid, tgid);
         }

         // 只处理进程(PID == TGID)
         if (pid == tgid) {
            //当pid==tgid时，表示是进程,进程数+1
            first->process_count++;
            // c1>0 表示是活动进程.c1是两个采样点的cputime的差值，如果大于0.表示cpu使用有增加
            //取消对send_data的判断
            if (c1 > 0)
            {
               //如果c1>0,活动进程+1
            first->active_process_count++;
            if (o >= count)
            {
               // 需要扩展内存
               os_data *new_os = alloc_r(os, (o + 1) * sizeof(os_data));
               if (new_os == NULL) {
                  perror("内存分配失败");
                  if (cmd) free(cmd);
                  continue;
               }
               os = new_os;
               memset(os + count, 0, 1 * sizeof(os_data));
               count++;
               first = &os[0];
            }
            
            struct pids_stack *p = Pids_reap->stacks[p1];
            char *c = cal_cpu(p);
            char *t = cal_time(p);
            char *m = cal_mem(p);
            os_data *od = &os[o];
            
            // 初始化进程数据
            init_parpids(od, p, c, t, m);
            
            // 计算该进程的子线程数量
            int child_count = 0;
            
            // 遍历找出属于该进程的所有线程
            for (p2 = 0; p2 < PIDSmaxt; p2++) {
               // 确保索引在有效范围内
               if (p2 < 0 || p2 >= PIDSmaxt || Pids_reap->stacks[p2] == NULL) {
                  continue;
               }
               
               int t_pid = Pids_reap->stacks[p2]->head[EU_PID].result.s_int;
               int t_tgid = Pids_reap->stacks[p2]->head[EU_TGD].result.s_int;
               int t_cpu = Pids_reap->stacks[p2]->head[EU_CPU].result.u_int;
               
               // 如果TGID与父进程PID匹配且PID不等于TGID且CPU使用率>0，则为活动线程
               if (t_tgid == pid && t_pid != t_tgid && t_cpu > 0) {
                  //活跃线程+1
                  first->active_thread_count++;
                  if (o1 >= count) {
                     // 需要扩展内存
                     os_data *new_os = alloc_r(os, (o1 + 1) * sizeof(os_data));
                     if (new_os == NULL) {
                        perror("内存分配失败");
                        continue;
                     }
                     os = new_os;
                     memset(os + o1, 0, 1 * sizeof(os_data));
                     count++;
                     
                     // 因为os可能已被重新分配，需要更新od指针
                     first = &os[0];
                  }
                  
                  struct pids_stack *thread_p = Pids_reap->stacks[p2];
                  char *thread_c = cal_cpu(thread_p);
                  char *thread_t = cal_time(thread_p);
                  char *thread_m = cal_mem(thread_p);
                  os_data *od1 = &os[o1];
                  
                  // 初始化线程数据
                  init_thrpids(od1, thread_p, thread_c, thread_t, thread_m);
                  o1++;
                  child_count++;
               }
            }
            
            // 设置该进程的活动线程数
            od->process_pid.active_threads = child_count;
            o++;
         }
      }
      
         // 释放命令字符串内存
         if (cmd != NULL) {
            free(cmd);
            Pids_reap->stacks[p1]->head[EU_CMD].result.str = NULL;
         }
      }
   } else {
      // 当enable_tasks_refresh为false时，跳过进程和线程处理
      // 设置默认值以避免未初始化变量
      o = 0;
      o1 = 0;
      count = Cpu_cnt + 1;  // 设置默认的count值
      printf("tasks_refresh功能已禁用，跳过进程和线程统计\n");
   }

   /* load1_old = load1_new;
   load5_old = load5_new;
   load15_old = load15_new; */
   // add max active process and thread

   o2 = (o > o1) ? (o > sum_cores ? o : sum_cores) : (o1 > sum_cores ? o1 : sum_cores);
   // 必须要跟count比较，因为有可能会小于count ，count才是最大�?
   o2 = (o2 > count) ? o2 : count;
   // lock wait sar
   //打印o,o1,o2,count的值 
   printf("111111111111111111:o: %d, o1: %d, o2: %d, count: %d\n", o, o1, o2, count);

   if (!abandon(aaa, ab))
   {
      /*
   puts("info.c : unlock sar tcp1 start...\n");
   pthread_cond_signal(&sar_rcond);
   pthread_cond_signal(&tc11);
   */

      // 主线程完成初始化工作
    
     

      // A线程：完成工作后到门禁1通知BC开始工作
      write_log("A线程(sys)：完成数据收集工作，到达门禁1通知BC线程...\n");

      // 初始化barriers（如果还没初始化）
      if (init_barriers() != 0) {
          write_log("A线程：初始化barriers失败\n");
          return;
      }

      int barrier_ret = efficient_barrier_wait(&barrier_a_done);
      if (barrier_ret < 0) {
          write_log("A线程：门禁1通知失败\n");
          return;
      }
      write_log("A线程：已通过门禁1，BC线程收到开始工作通知\n");

      // A线程：立刻到门禁2等待BC完成工作
      write_log("A线程：到达门禁2，等待BC线程完成工作...\n");
      barrier_ret = efficient_barrier_wait(&barrier_bc_done);
      if (barrier_ret < 0) {
          write_log("A线程：门禁2等待失败\n");
          return;
      }
      write_log("A线程：通过门禁2，BC线程已完成工作，继续执行\n");
   

      /*
    while( ths!=2 ){
        //如果release1 已经�?了，但是代码刚好执行到这里，会一直在while�?
       if ( release1 == 3) break;
       if ( sigint_caught==1 && sar_lock1==1) pthread_cond_signal(&sar_rcond);
       if ( pd->break_loop==1 && tcp1_lock==1) pthread_cond_signal(&tc11);
       if ( wait1==1 && c_tcp1_lock1==1) pthread_cond_signal(&cc11);
        // printf(".");
         sleep(1);
         puts("wait 1s");

    }
    */
     
   }
}

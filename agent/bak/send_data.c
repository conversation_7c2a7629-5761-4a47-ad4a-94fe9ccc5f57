#include <stdio.h>
#include <stdlib.h>
#include "send.h"
#include <string.h>
#include "top.h"
#include <unistd.h>
#include <sys/socket.h>
#include <netinet/in.h>
#include <signal.h>
#include "err.h"
#include "readproc.h"
#include <fcntl.h>
#include <pthread.h>
#include "misc.h"
#include "meminfo.h"
#include <limits.h>
int sock;
void handle_sigint(int sig) {
    write(1, "Caught SIGINT, releasing socket...\n", 32);
    // 这里应该有释放socket的代码，例如: close(sockfd);
    //close(sock);
    exit(0); // 立即退出程序
}
/*
void ignore_sigpipe() {
    struct sigaction act;
    act.sa_handler = SIG_IGN;
    sigemptyset(&act.sa_mask);
    act.sa_flags = 0;
    if (sigaction(SIGPIPE, &act, 0) == -1) {
        perror("sigaction");
        exit(EXIT_FAILURE);
    }
}
*/
struct utlbuf_s {
    char *buf;     // dynamically grown buffer
    int   siz;     // current len of the above
} utlbuf_s;

#define PROCPATHLEN 64  
static int file2str(const char *directory, const char *what, struct utlbuf_s *ub) {
 #define buffGRW 1024
    char path[PROCPATHLEN];
    int fd, num, tot_read = 0, len;

    /* on first use we preallocate a buffer of minimum size to emulate
       former 'local static' behavior -- even if this read fails, that
       buffer will likely soon be used for another subdirectory anyway
       ( besides, with the calloc call we will never need use memcpy ) */
    if (ub->buf) ub->buf[0] = '\0';
    else {
        ub->buf = calloc(1, (ub->siz = buffGRW));
        if (!ub->buf) return -1;
    }
    len = snprintf(path, sizeof path, "%s/%s", directory, what);
    if (len <= 0 || (size_t)len >= sizeof path) return -1;
    if (-1 == (fd = open(path, O_RDONLY, 0))) return -1;
    while (0 < (num = read(fd, ub->buf + tot_read, ub->siz - tot_read))) {
        tot_read += num;
        if (tot_read < ub->siz) break;
        if (ub->siz >= INT_MAX - buffGRW) {
            tot_read--;
            break;
        }
        if (!(ub->buf = realloc(ub->buf, (ub->siz += buffGRW)))) {
            close(fd);
            return -1;
        }
    };
    ub->buf[tot_read] = '\0';
    close(fd);
    if (tot_read < 1) return -1;
    return tot_read;
 #undef buffGRW
}

static int stat2proc (const char *S, proc_t *restrict P) {
    char buf[64], raw[64];
    size_t num;
    char *tmp;

//ENTER(0x160);

    /* fill in default values for older kernels */
    P->processor = 0;
    P->rtprio = -1;
    P->sched = -1;
    P->nlwp = 0;

    S = strchr(S, '(');
    if (!S) return 0;
    S++;
    tmp = strrchr(S, ')');
    if (!tmp || !tmp[1]) return 0;
#ifdef FALSE_THREADS
    if (!IS_THREAD(P)) {
#endif
    if (!P->cmd) {
       num = tmp - S;
       memcpy(raw, S, num);
       raw[num] = '\0';
       escape_str(buf, raw, sizeof(buf));
       if (!(P->cmd = strdup(buf))) return 1;
    }
#ifdef FALSE_THREADS
     }
#endif
    S = tmp + 2;                 // skip ") "

    sscanf(S,
       "%c "                      // state
       "%d %d %d %d %d "          // ppid, pgrp, sid, tty_nr, tty_pgrp
       "%lu %lu %lu %lu %lu "     // flags, min_flt, cmin_flt, maj_flt, cmaj_flt
       "%llu %llu %llu %llu "     // utime, stime, cutime, cstime
       "%d %d "                   // priority, nice
       "%d "                      // num_threads
       "%lu "                     // 'alarm' == it_real_value (obsolete, always 0)
       "%llu "                    // start_time
       "%lu "                     // vsize
       "%lu "                     // rss
       "%lu %lu %lu %lu %lu %lu " // rsslim, start_code, end_code, start_stack, esp, eip
       "%*s %*s %*s %*s "         // pending, blocked, sigign, sigcatch                      <=== DISCARDED
       "%lu %*u %*u "             // 0 (former wchan), 0, 0                                  <=== Placeholders only
       "%d %d "                   // exit_signal, task_cpu
       "%d %d "                   // rt_priority, policy (sched)
       "%llu %llu %llu",          // blkio_ticks, gtime, cgtime
       &P->state,
       &P->ppid, &P->pgrp, &P->session, &P->tty, &P->tpgid,
       &P->flags, &P->min_flt, &P->cmin_flt, &P->maj_flt, &P->cmaj_flt,
       &P->utime, &P->stime, &P->cutime, &P->cstime,
       &P->priority, &P->nice,
       &P->nlwp,
       &P->alarm,
       &P->start_time,
       &P->vsize,
       &P->rss,
       &P->rss_rlim, &P->start_code, &P->end_code, &P->start_stack, &P->kstk_esp, &P->kstk_eip,
/*     P->signal, P->blocked, P->sigignore, P->sigcatch,   */ /* can't use */
       &P->wchan, /* &P->nswap, &P->cnswap, */  /* nswap and cnswap dead for 2.4.xx and up */
/* -- Linux 2.0.35 ends here -- */
       &P->exit_signal, &P->processor,  /* 2.2.1 ends with "exit_signal" */
/* -- Linux 2.2.8 to 2.5.17 end here -- */
       &P->rtprio, &P->sched,  /* both added to 2.5.18 */
       &P->blkio_tics, &P->gtime, &P->cgtime
    );

    if(!P->nlwp)
      P->nlwp = 1;

    return 0;
LEAVE(0x160);
}

static void statm2proc(const char *s, proc_t *restrict P) {
    sscanf(s, "%lu %lu %lu %lu %lu %lu %lu",
           &P->size, &P->resident, &P->share,
           &P->trs, &P->lrs, &P->drs, &P->dt);
}

static inline void free_acquired (proc_t *p) {
    /*
     * here we free those items that might exist even when not explicitly |
     * requested by our caller.  it is expected that pid.c will then free |
     * any remaining dynamic memory which might be dangling off a proc_t. | */
    if (p->cgname)   free(p->cgname);
    if (p->cgroup)   free(p->cgroup);
    if (p->cmd)      free(p->cmd);
    if (p->sd_mach)  free(p->sd_mach);
    if (p->sd_ouid)  free(p->sd_ouid);
    if (p->sd_seat)  free(p->sd_seat);
    if (p->sd_sess)  free(p->sd_sess);
    if (p->sd_slice) free(p->sd_slice);
    if (p->sd_unit)  free(p->sd_unit);
    if (p->sd_uunit) free(p->sd_uunit);
    if (p->supgid)   free(p->supgid);

    memset(p, '\0', sizeof(proc_t));
}



int check_self(){
  for(;;){
    struct utlbuf_s ub = { NULL, 0 };
    int rc = 0;
    proc_t p;
    double uptime_cur;
    float et;
    static double uptime_sav;

    memset(&p, 0, sizeof(proc_t));
    if(file2str("/proc/self", "stat", &ub) == -1){
        fprintf(stderr, "Error, do this: mount -t proc proc /proc\n");
        _exit(47);
    }
    rc = stat2proc(ub.buf, &p); // parse /proc/self/stat
    int cpu_sav=0; 
    cpu_sav=p.utime+p.stime;

    procps_uptime(&uptime_cur, NULL);
    et = uptime_cur - uptime_sav;
    if (et < 0.01) et = 0.005;
    uptime_sav = uptime_cur;
    long Hertz = procps_hertz_get();
      // if in Solaris mode, adjust our scaling for all cpus
    float Frame_etscale1 = 100.0f / ((float)Hertz * (float)et * 1);
    free_acquired(&p);   
    free(ub.buf);
    sleep(5);
    ub.buf=NULL;
    ub.siz=0;
       memset(&p, 0, sizeof(proc_t));
    if(file2str("/proc/self", "stat", &ub) == -1){
        fprintf(stderr, "Error, do this: mount -t proc proc /proc\n");
        _exit(47);
    }
    rc = stat2proc(ub.buf, &p);
    float cpu_time=(float)p.utime+p.stime-cpu_sav;
    
    procps_uptime(&uptime_cur, NULL);
    et = uptime_cur - uptime_sav;
    if (et < 0.01) et = 0.005;
    uptime_sav = uptime_cur;
    Hertz = procps_hertz_get();
      // if in Solaris mode, adjust our scaling for all cpus
    Frame_etscale1 = 100.0f / ((float)Hertz * (float)et * 1);   

    cpu_time *= Frame_etscale1;
    //char *buf1=calloc(1,7);

    //snprintf(buf1, sizeof(buf1), "%#.1f", cpu_time);
    printf("send_data cpu=%.1f\n",cpu_time);
    fflush(stdout);
    if ( cpu_time > 90.0){

	 char *errlog="send_data process cpu usage% exceed 10% ,program exit";
                         err(errlog,get_time());
                         exit(1);	

    }
 
    free(ub.buf);
    ub.buf=NULL;
    ub.siz=0; 
    

    if (file2str("/proc/self", "statm", &ub) != -1){
          statm2proc(ub.buf, &p);
    }

     int fd=open("/proc/meminfo", O_RDONLY);
     lseek(fd, 0L, SEEK_SET);
     char *head, *tail;
     int size;
     char buf[100]; 
     
    for (;;) {
        if ((size = read(fd, buf, sizeof(buf)-1)) < 0) {
            return 1;
        }
        break;
    }
    if (size == 0) {
        return 1;
    }
    buf[size] = '\0';

    head = buf;


        tail = strchr(head, ':');
        *tail = '\0';
        head = tail + 1;
        float a2 = strtoul(head, NULL, 10);


    float mem_time=p.resident << 2;
    float m_p;
    m_p=mem_time * 100.0 /a2;
    //char *buf2=calloc(1,7);

    //snprintf(buf2, sizeof(buf2), "%#.1f", m_p);
    printf("send_data mem=%.1f mem_time=%.1f a2=%.1f\n",m_p,mem_time,a2);
    fflush(stdout);
    if ( m_p > 10.0){

         char *errlog="send_data process mem usage% exceed 1% ,program exit";
                         err(errlog,get_time());
                         exit(1);

    } 
	


    free_acquired(&p);
    free(ub.buf);
    close(fd);
    //free(buf1);
    //free(buf2);
}



}
 
int main()
{
 /*
  struct sigaction sa;
    sa.sa_handler = &handle_sigint;
    sigemptyset(&sa.sa_mask);
    sa.sa_flags = 0;
    sigaction(SIGINT, &sa, NULL);
*/ 
	//check self
	pthread_t th;
//	pthread_create(&th, NULL,&check_self, NULL);
      
  while (1)
  {
  top2:
 //  ignore_sigpipe();
  sock=get_sock();
  if (connect1() != 0)
  {
     char *errlog="connect failed, remote server is offline!";
        err(errlog,get_time());	
      close(sock);
      sleep(3);
      continue;
  } 
  top:
  //arr= (os_data*)malloc((max_cores+1)*sizeof(os_data));
  for(;;){ 
	if ( os == NULL )	sys();
	if ( !abandon(aaa,ab) ){
		break;
	}
	free(os);
	os=NULL;
  }
  //memset(arr,0,(max_cores+1)*sizeof(os_data));
/*
  printf("sizeof(buffer).NO1=%d\n",sizeof(buffer));
  printf("strlen(arr->avg_load11)=%d\n",strlen(arr->avg_load11));
  printf("strlen(arr->time1)=%d\n",strlen(arr->time1));
*/
  //memcpy(&buffer,os, (sum_cores+1)*sizeof(os_data));
/*
  printf("sizeof(buffer)NO2=%d\n",sizeof(buffer));
  printf("load1=%s\n",arr->avg_load11);
  printf("active1=%d\n",arr->active1);
  printf("time1=%s\n",arr->time1);
*/
  puts("###################check cpu 0 1 ....");
  int jj;
      for (jj=0;jj < sum_cores; jj++){
         os_data *arr1=&os[jj];
        printf("send data arr1->cpu_p.u_frme_p=%s u_frme=%ld u_f=%ld sum_user=%s sum_sys=%s sum_total=%s\n",arr1->cpu_p.u_frme_p,arr1->cpu_p.u_frme,arr1->cpu_p.u_f,arr1->cpu_p.sum_user_p,arr1->cpu_p.sum_sys_p,arr1->cpu_p.sum_total_p);
	fflush(stdout);
	
      }
  puts("##################check load active time##########");
  os_data *cpu_sum=&os[jj];
  printf("load1=%s active1=%d time1=%s \n",cpu_sum->avg_load11,cpu_sum->active1,cpu_sum->time1);
  fflush(stdout);
  puts("##################check cpu_sum#################");
  printf("send data cpu_sum->cpu_p.u_frme_p=%s u_frme=%ld u_f=%ld sum_user=%s sum_sys=%s sum_total=%s\n",cpu_sum->cpu_p.u_frme_p,cpu_sum->cpu_p.u_frme,cpu_sum->cpu_p.u_f,cpu_sum->cpu_p.sum_user_p,cpu_sum->cpu_p.sum_sys_p,cpu_sum->cpu_p.sum_total_p);
   fflush(stdout);
	puts("################check mem############");
	printf("send data KB_main_free=%lu KB_main_used=%lu KB_main_cached=%lu KB_main_available=%lu pcnt_tot=%s swp_USE_p=%s KB_main_total=%lu KB_swap_free=%lu\n",cpu_sum->meminfo.KB_main_free,cpu_sum->meminfo.KB_main_used,cpu_sum->meminfo.KB_main_cached,cpu_sum->meminfo.KB_main_available,cpu_sum->meminfo.pcnt_tot,cpu_sum->meminfo.swp_USE_p,cpu_sum->meminfo.KB_main_total,cpu_sum->meminfo.KB_swap_free);	
	fflush(stdout);
	

	//check active process
	puts("#############check active process##############");
        os_data *om1=&os[0];
        printf("total_count=%d  count_RUNING=%d count_sleeping=%d count_stopped=%d count_zombie=%d\n",\
        om1->process_count,om1->process_running_count,om1->process_sleeping_count,om1->process_stopped_count,\
        om1->process_zombie_count);
        puts("+++++++++++++++++++++++++++++++++++++++++++++++++++++++++\n");
	


	      int mm;
        for(mm=0; mm<o;mm++){
        os_data *osm=&os[mm];
/*
	if ( strcmp(osm->process_pid.COMMAND,"send_data") == 0){
		if ( strcmp(osm->process_pid.CPU_P,"3.0") > 0 || strcmp(osm->process_pid.MEM_P,"1.0") > 0 )
		{
			 char *errlog="send_data process cpu usage% exceed 1% ,program exit";
        		 err(errlog,get_time());
			 exit(1);

			
		}



	}
*/
  printf("sizeof os_data=%d threads=%d active_threads=%d pid=%d tgid=%lu user=%s PR=%d NI=%d VIRT=%lu\
 RES=%lu SHR=%lu stat=%c CPU_P=%s MEM_P=%s \
CPU_TIME=%s COMMAND=%s\n",sizeof(os_data),osm->process_pid.threads,osm->process_pid.active_threads,\
osm->process_pid.PID,\
osm->process_pid.TGID,osm->process_pid.USER,\
osm->process_pid.PR,osm->process_pid.NI,osm->process_pid.VIRT,osm->process_pid.RES,osm->process_pid.SHR,\
osm->process_pid.S,osm->process_pid.CPU_P,osm->process_pid.MEM_P,osm->process_pid.CPU_TIME,\
osm->process_pid.COMMAND);
	puts("+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++\n");
	fflush(stdout);
	}
	
	//check active threads
	puts("#############check active threads##############");
        os_data *os11=&os[0];
        printf("total_count=%d  count_RUNING=%d count_sleeping=%d count_stopped=%d count_zombie=%d\n",\
        os11->thread_count,os11->thread_running_count,os11->thread_sleeping_count,os11->thread_stopped_count,\
        os11->thread_zombie_count);
        puts("+++++++++++++++++++++++++++++++++++++++++++++++++++++++++\n");
	int oo;	
	for(oo=0; oo<o1;oo++){
	os_data *os1=&os[oo];
  printf("sizeof os_data=%d pid=%d tgid=%lu user=%s PR=%d NI=%d VIRT=%lu RES=%lu SHR=%lu stat=%c CPU_P=%s MEM_P=%s \
CPU_TIME=%s COMMAND=%s\n",sizeof(os_data),\
os1->thread_pid.PID,os1->thread_pid.TGID,os1->thread_pid.USER,os1->thread_pid.PR,os1->thread_pid.NI,\
os1->thread_pid.VIRT,\
os1->thread_pid.RES,os1->thread_pid.SHR,os1->thread_pid.S,os1->thread_pid.CPU_P,os1->thread_pid.MEM_P,\
os1->thread_pid.CPU_TIME,os1->thread_pid.COMMAND);
puts("+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++\n");
	fflush(stdout);
      }
      puts("*********************End***************");
      fflush(stdout);

        printf("o=%d o1=%d sum_cores=%d\n",o,o1,sum_cores);
        fflush(stdout);
        int o2=(o > o1) ? (o > sum_cores ? o : sum_cores) : (o1 > sum_cores ? o1 : sum_cores);


    // 发送消息
    if (send(sock,os,o2*sizeof(os_data), 0) < 0) {
        printf("Send failed\n");
	fflush(stdout);
        char *errlog="send failed, remote server is offline!";
        err(errlog,get_time());
        	free(os);
		os=NULL;
        //close(sock);
        continue;
       // return 1;

    }
    puts("waiting server send successfull signal..\n");
    char s[1];
    while (recv(sock, &s, sizeof(s), 0) > 0) {   
       //printf("s=%c\n",s[0]); 
       if (s[0] == '1')
       {
         s[0]='\0';
         printf("server already apply data.coninue..\n");
          free(os);
         os=NULL;
         goto top; 
       }
       if (s[0] == 'x')
       {
	  puts("server pool is exhausted.please rtry!\n");
          close(sock); 
          sleep(1);
          goto top2 ;
          
       }

    }

    
    if ((recv(sock, &s, sizeof(s), 0)) == 0) {
        printf("Client closed connection\n");
	fflush(stdout);
        char *errlog=" remote server is offline!";
       // char  *errlog="connect failed, remote server is offline!";
        err(errlog,get_time());
        close(sock);
    } else {
        perror("recv failed");
    }
    free(os);
    os=NULL;  

  }
  close(sock);
  
  return 0;

}

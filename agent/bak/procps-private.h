/*
 * libprocps - Library to read proc filesystem
 *
 * This library is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston, MA  02110-1301  USA
 */
#ifndef PROCPS_PRIVATE_H
#define PROCPS_PRIVATE_H

#define PROCPS_EXPORT __attribute__ ((visibility("default")))

#define STRINGIFY_ARG(a)	#a
#define STRINGIFY(a)		STRINGIFY_ARG(a)

#define MAXTABLE(t)		(int)(sizeof(t) / sizeof(t[0]))

#endif

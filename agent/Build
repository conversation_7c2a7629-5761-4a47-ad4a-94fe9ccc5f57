libperf-y += info.o
libperf-y += sysinfo.o
libperf-y += send.o
libperf-y += send_data.o
libperf-y += err.o
libperf-y += stat.o
libperf-y += meminfo.o
libperf-y += version.o
libperf-y += pids.o
libperf-y += top_nls.o
libperf-y += namespace.o
libperf-y += devname.o
libperf-y += escape.o
libperf-y += wchan.o
libperf-y += pwcache.o
libperf-y += uptime.o
libperf-y += readproc.o
libperf-y += ioconf.o

#c_flags  := $(filter-out  -I/app/perf/perf-4.19.0/tools/include/, $(c_flags))
#c_flags :=$(subst -I/app/perf/perf-4.19.0/tools/include/ ,,$(c_flags))
#c_flags :=$(subst -Wp,-<PERSON>,,,$(c_flags))
#c_flags :=$(subst ,,-<PERSON>,-<PERSON>,..d -<PERSON>,-MT,,$(c_flags))
#c_flags :=
#c_flags +=--std=gnu99
c_flags +=-I/usr/include/
#c_flags +=-D_GNU_SOURCE
#c_flags :=$(subst -I/app/perf/perf-4.19.0/tools/arch/x86/ ,,$(c_flags))
#c_flaggs -=-I/app/perf/perf-4.19.0/tools/include/
#c_flags +=-I/usr/include/
#dep-cmd :=printf 'xxxxx'
$(info  cccccc_flags=$(c_flags))
